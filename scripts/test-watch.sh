#!/bin/bash

# OccuSolve Test Watch Script
# Runs tests in watch mode for development

set -e

echo "🔍 Starting OccuSolve Test Watch Mode"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    print_warning "node_modules not found. Installing dependencies..."
    npm install
fi

# Parse command line arguments
WATCH_TYPE="unit"
COVERAGE=false
VERBOSE=false

while [[ $# -gt 0 ]]; do
    case $1 in
        --type)
            WATCH_TYPE="$2"
            shift 2
            ;;
        --coverage)
            COVERAGE=true
            shift
            ;;
        --verbose)
            VERBOSE=true
            shift
            ;;
        --help)
            echo "Usage: $0 [OPTIONS]"
            echo ""
            echo "Options:"
            echo "  --type TYPE     Type of tests to watch (unit|component|e2e) [default: unit]"
            echo "  --coverage      Run with coverage reporting"
            echo "  --verbose       Enable verbose output"
            echo "  --help          Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            echo "Use --help for usage information"
            exit 1
            ;;
    esac
done

# Function to run unit tests in watch mode
run_unit_watch() {
    print_status "Starting unit tests in watch mode..."
    
    if [ "$COVERAGE" = true ]; then
        print_status "Coverage reporting enabled"
        npm run test:unit:watch -- --coverage --watchAll
    else
        npm run test:unit:watch
    fi
}

# Function to run component tests in watch mode
run_component_watch() {
    print_status "Starting component tests in watch mode..."
    
    # Check if Cypress is installed
    if ! command -v cypress &> /dev/null; then
        print_warning "Cypress not found. Installing..."
        npm install cypress --save-dev
    fi
    
    print_status "Opening Cypress component test runner..."
    npm run test:component
}

# Function to run E2E tests in watch mode
run_e2e_watch() {
    print_status "Starting E2E tests in watch mode..."
    
    # Check if Cypress is installed
    if ! command -v cypress &> /dev/null; then
        print_warning "Cypress not found. Installing..."
        npm install cypress --save-dev
    fi
    
    print_status "Opening Cypress E2E test runner..."
    npm run test:e2e
}

# Main execution
print_status "Watch type: $WATCH_TYPE"

case $WATCH_TYPE in
    unit)
        run_unit_watch
        ;;
    component)
        run_component_watch
        ;;
    e2e)
        run_e2e_watch
        ;;
    *)
        print_error "Invalid watch type: $WATCH_TYPE"
        print_error "Valid types: unit, component, e2e"
        exit 1
        ;;
esac

print_success "Test watch session completed"
