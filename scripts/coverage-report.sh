#!/bin/bash

# OccuSolve Coverage Report Generator
# Generates comprehensive coverage reports and analysis

set -e

echo "📊 Generating OccuSolve Coverage Report"
echo "======================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    print_error "package.json not found. Please run this script from the project root."
    exit 1
fi

# Create reports directory if it doesn't exist
mkdir -p reports

# Run tests with coverage
print_status "Running unit tests with coverage..."
npm run test:unit:coverage

# Check if coverage was generated
if [ ! -d "test/jest/coverage" ]; then
    print_error "Coverage directory not found. Tests may have failed."
    exit 1
fi

# Copy coverage to reports directory
print_status "Copying coverage reports to reports directory..."
cp -r test/jest/coverage reports/unit-coverage

# Generate coverage summary
print_status "Generating coverage summary..."
cat > reports/coverage-summary.md << EOF
# Coverage Report Summary

Generated on: $(date)

## Overall Coverage

$(cat test/jest/coverage/coverage-summary.txt 2>/dev/null || echo "Coverage summary not available")

## Coverage Thresholds

- **Statements**: 17% (Current threshold)
- **Branches**: 0.2% (Current threshold)
- **Functions**: 0.7% (Current threshold)
- **Lines**: 17% (Current threshold)

## Coverage Reports

- **HTML Report**: [reports/unit-coverage/lcov-report/index.html](unit-coverage/lcov-report/index.html)
- **LCOV Report**: [reports/unit-coverage/lcov.info](unit-coverage/lcov.info)
- **JSON Report**: [reports/unit-coverage/coverage-final.json](unit-coverage/coverage-final.json)

## Recommendations

### High Priority (Add tests for these files)
- Components with 0% coverage
- Critical business logic files
- Form validation components

### Medium Priority
- Utility functions
- Helper modules
- Configuration files

### Low Priority
- Simple presentational components
- Mock files
- Test utilities

## Next Steps

1. **Immediate**: Add tests for critical components with 0% coverage
2. **Short-term**: Increase overall coverage to 30%
3. **Long-term**: Achieve 80% coverage for business-critical code

EOF

# Extract uncovered files
print_status "Analyzing uncovered files..."
if [ -f "test/jest/coverage/lcov.info" ]; then
    # Extract files with 0% coverage
    grep -E "^SF:" test/jest/coverage/lcov.info | sed 's/SF://' | while read -r file; do
        if ! grep -q "LH:0" test/jest/coverage/lcov.info; then
            echo "- $file" >> reports/uncovered-files.txt
        fi
    done
fi

# Generate test metrics
print_status "Generating test metrics..."
cat > reports/test-metrics.md << EOF
# Test Metrics Report

Generated on: $(date)

## Test Suite Statistics

$(npm run test:unit 2>&1 | grep -E "(Test Suites|Tests|Snapshots|Time)" | tail -4 || echo "Test metrics not available")

## Test File Count

- **Unit Tests**: $(find test/jest/__tests__ -name "*.spec.js" | wc -l | tr -d ' ')
- **Component Tests**: $(find test/cypress/component -name "*.spec.js" 2>/dev/null | wc -l | tr -d ' ')
- **E2E Tests**: $(find test/cypress/integration -name "*.spec.js" 2>/dev/null | wc -l | tr -d ' ')

## Coverage by Category

### Components
$(grep -E "src/components" test/jest/coverage/lcov.info 2>/dev/null | wc -l | tr -d ' ') files covered

### Pages
$(grep -E "src/pages" test/jest/coverage/lcov.info 2>/dev/null | wc -l | tr -d ' ') files covered

### Utilities
$(grep -E "src/utils" test/jest/coverage/lcov.info 2>/dev/null | wc -l | tr -d ' ') files covered

## Test Quality Indicators

- **Test-to-Code Ratio**: $(echo "scale=2; $(find test -name "*.spec.js" | wc -l) / $(find src -name "*.vue" -o -name "*.js" | wc -l)" | bc 2>/dev/null || echo "N/A")
- **Average Test File Size**: $(find test -name "*.spec.js" -exec wc -l {} + 2>/dev/null | tail -1 | awk '{print int($1/NR)}' || echo "N/A") lines

EOF

# Open coverage report if requested
if [ "$1" = "--open" ]; then
    print_status "Opening coverage report in browser..."
    if command -v open &> /dev/null; then
        open reports/unit-coverage/lcov-report/index.html
    elif command -v xdg-open &> /dev/null; then
        xdg-open reports/unit-coverage/lcov-report/index.html
    else
        print_warning "Cannot open browser automatically. Please open reports/unit-coverage/lcov-report/index.html manually."
    fi
fi

print_success "Coverage report generated successfully!"
print_status "Reports available in the 'reports' directory:"
print_status "- reports/unit-coverage/lcov-report/index.html (HTML report)"
print_status "- reports/coverage-summary.md (Summary)"
print_status "- reports/test-metrics.md (Metrics)"

echo ""
print_status "To view the HTML report, run:"
print_status "  open reports/unit-coverage/lcov-report/index.html"
