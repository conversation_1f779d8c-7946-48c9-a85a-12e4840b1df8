# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

OccuSolve is a medical records management system built with Vue 3, Quasar Framework, and GraphQL. It manages occupational health assessments including patients, companies, medical examinations, drug screenings, audiometry, spirometry, vision screenings, and physical assessments.

## Development Commands

### Setup and Development
```bash
# Install dependencies
npm install
# or
yarn

# Start development server (opens browser automatically)
quasar dev

# Build for production
quasar build
```

### Code Quality
```bash
# Lint files
npm run lint
# or
yarn lint

# Format code
npm run format
# or
yarn format
```

### Testing
```bash
# Run unit tests
npm run test:unit

# Run unit tests in watch mode
npm run test:unit:watch

# Run unit tests with coverage
npm run test:unit:coverage

# Serve coverage report
npm run serve:test:coverage

# Run E2E tests (development)
npm run test:e2e

# Run E2E tests (CI)
npm run test:e2e:ci

# Run component tests
npm run test:component
```

## Architecture

### Frontend Stack
- **Vue 3** with Composition API
- **Quasar Framework** for UI components and build system
- **Vue Router** for routing
- **Apollo Client** for GraphQL state management
- **Tailwind CSS** + **Quasar** for styling
- **Vuelidate** for form validation
- **Heroicons** for icons

### Key Architectural Patterns

**GraphQL Integration:**
- Apollo Client configured in `src/boot/apollo.js`
- Automatic authentication via token cookies
- Offline queue and retry functionality
- Persistent cache using LocalForage

**Component Structure:**
- Pages in `src/pages/` follow naming pattern: `[Entity][Action]Page.vue`
- Reusable components in `src/components/`
- Custom Quasar component wrappers (QuasarButton, QuasarDialog, etc.)

**Routing:**
- Nested routes under MainLayout for authenticated pages
- Authentication-free login page
- Pattern: `/patients/:id/[sub-resource]/[action]`

### Core Entities and Workflows

**Primary Entities:**
- **Patients** - Core entity with personal information
- **Companies** - Organizations that employ patients  
- **Medicals** - Medical examination records (draft/signed-off states)
- **Clinics** - Medical facilities

**Assessment Types:**
- **Audio** - Audiometry tests
- **Spiro** - Spirometry tests  
- **Vision Screenings** - Eye examinations
- **Physical Assessments** - Physical examinations
- **Drug Screenings** - Drug testing
- **Annexure Three** - Specific medical forms

**Navigation Flow:**
1. Patient management (index, create, edit, show)
2. From patient detail → create assessments
3. Assessment management (index, create, edit, show)
4. Medical records compilation from assessments

### Environment Configuration

The app expects `VITE_GRAPHQL_ENDPOINT` environment variable for GraphQL API connection.

### Development Notes

- Boot files in `src/boot/` initialize: Apollo GraphQL, VCalendar, input facades, MirageJS (testing)
- Mobile-responsive design with sidebar navigation
- Authentication handled via cookies (`occusolve-token`)
- Offline-first architecture with request queuing
- Component auto-import via unplugin-vue-components
- GraphQL schema available in `src/gql/schema.gql`

### Testing Setup

- **Jest** for unit testing with Vue Test Utils
- **Cypress** for E2E and component testing
- **MirageJS** for API mocking in development
- Test files follow pattern: `*.spec.js` or `*.test.js`
- Coverage reports generated in `test/jest/coverage/`