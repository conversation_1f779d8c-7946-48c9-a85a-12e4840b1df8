# Apollo Cache Display Fix Implementation Plan

## ☐ **Phase 1: Fix Apollo Cache Display & Implement Cache-First Strategy**

**Affected Files:**
- ``src/boot/apollo.js`` - Add cache debugging, persistence verification, and error recovery
- ``src/pages/PatientIndexPage.vue`` - Implement cache-first pattern with proper loading states
- ``src/pages/MedicalIndexPage.vue`` - Implement cache-first pattern with proper loading states
- ``src/pages/CompanyIndexPage.vue`` - Implement cache-first pattern with proper loading states
- ``src/composables/use-cache-first-query.js`` - New composable for consistent cache behavior

### src/boot/apollo.js

Replace lines 89-96 with:

```javascript
const cache = new InMemoryCache({
  typePolicies: {
    Query: {
      fields: {
        patients: {
          keyArgs: ['organisationId'],
        },
        medicals: {
          keyArgs: ['organisationId'],
        },
        companies: {
          keyArgs: ['organisationId'],
        }
      }
    }
  }
})

// Persistence with verification and error handling
try {
  console.log('[Apollo Cache] Starting cache persistence...')

  await persistCache({
    cache,
    storage: new LocalForageWrapper(localforage),
    debug: process.env.DEV,
    maxSize: false, // Disable size limit for medical data
  })

  // Verify cache was restored
  const cacheData = cache.extract()
  const cacheSize = Object.keys(cacheData).length

  if (cacheSize > 0) {
    console.log(`[Apollo Cache] Successfully restored ${cacheSize} cached entities`)

    // Log sample of cached data in dev mode
    if (process.env.DEV) {
      const samples = Object.keys(cacheData).slice(0, 5)
      console.log('[Apollo Cache] Sample cached keys:', samples)
    }
  } else {
    console.log('[Apollo Cache] No cached data found, starting fresh')
  }

  // Verify LocalForage is working
  const testKey = 'apollo-cache-test'
  await localforage.setItem(testKey, { test: true })
  const testValue = await localforage.getItem(testKey)

  if (testValue?.test === true) {
    console.log('[Apollo Cache] LocalForage persistence verified')
    await localforage.removeItem(testKey)
  } else {
    console.warn('[Apollo Cache] LocalForage persistence test failed')
  }

} catch (error) {
  console.error('[Apollo Cache] Persistence failed, using memory-only cache:', error)

  // Clear potentially corrupted cache
  try {
    await localforage.removeItem('apollo-cache-persist')
    console.log('[Apollo Cache] Cleared corrupted cache data')
  } catch (clearError) {
    console.error('[Apollo Cache] Failed to clear cache:', clearError)
  }
}
```

### src/composables/use-cache-first-query.js

Create new file:

```javascript
import { useQuery } from '@vue/apollo-composable'
import { computed, ref, watchEffect, onMounted } from 'vue'

export function useCacheFirstQuery(query, variables, options = {}) {
  const hasCachedData = ref(false)
  const cacheError = ref(null)

  // Start with cache-first to show cached data immediately
  const queryOptions = {
    ...options,
    fetchPolicy: 'cache-first',
    nextFetchPolicy: 'cache-and-network', // Background refresh after cache
    notifyOnNetworkStatusChange: true,
    errorPolicy: 'all', // Continue showing cached data even if network fails
  }

  const { result, loading, error, refetch, networkStatus } = useQuery(
    query,
    variables,
    queryOptions
  )

  // Handle cache read failures with automatic network fallback
  onMounted(async () => {
    try {
      // Attempt to read from cache
      const client = apolloClient // Will need to be imported or injected
      const cachedData = client.readQuery({
        query,
        variables,
      })

      if (cachedData) {
        hasCachedData.value = true
        console.log('[Cache] Found cached data for query')
      }
    } catch (err) {
      console.warn('[Cache] Failed to read from cache, falling back to network:', err)
      cacheError.value = err

      // Force network request if cache read fails
      try {
        await refetch()
      } catch (refetchError) {
        console.error('[Cache] Network fallback also failed:', refetchError)
      }
    }
  })

  // Track if we have cached data
  watchEffect(() => {
    if (result.value && !loading.value) {
      hasCachedData.value = true
      cacheError.value = null // Clear any cache errors once we have data
    }
  })

  // Compute loading states
  const isInitialLoad = computed(() => loading.value && !hasCachedData.value)
  const isRefreshing = computed(() => loading.value && hasCachedData.value)
  const isCacheError = computed(() => !!cacheError.value && !result.value)

  // Log cache status in development
  if (process.env.DEV) {
    watchEffect(() => {
      console.log('[Cache Query Status]', {
        hasCachedData: hasCachedData.value,
        isInitialLoad: isInitialLoad.value,
        isRefreshing: isRefreshing.value,
        networkStatus: networkStatus.value,
        hasError: !!error.value,
        cacheError: cacheError.value,
      })
    })
  }

  return {
    result,
    loading: isInitialLoad,
    isRefreshing,
    isCacheError,
    error: computed(() => error.value || cacheError.value),
    refetch,
    hasCachedData,
  }
}
```

### src/pages/PatientIndexPage.vue

Replace lines 248-320:

```javascript
import { useCacheFirstQuery } from 'src/composables/use-cache-first-query'
import { ArrowPathIcon } from '@heroicons/vue/24/outline'

const PATIENT_QUERY = gql`
  query listPatients($id: ID!) {
    patients(organisationId: $id) {
      id
      fullName
      phoneNumber
      identificationNumber
      dob
    }
  }
`;

const {
  result,
  loading,
  isRefreshing,
  isCacheError,
  error,
  refetch,
  hasCachedData
} = useCacheFirstQuery(PATIENT_QUERY, { id: 1 })

const patients = computed(() => {
  return result.value?.patients || []
})

const patientCount = computed(() => {
  return result.value?.patients?.length || 0
})

// Log cache status in development
if (process.env.DEV) {
  watch([loading, isRefreshing, hasCachedData], ([load, refresh, cached]) => {
    console.log('[PatientIndexPage] Cache Status:', {
      loading: load,
      refreshing: refresh,
      hasCached: cached,
      patientCount: patientCount.value
    })
  })
}
```

Replace template lines 4-5:

```vue
<div v-if="loading" class="flex justify-center p-8">
  <div class="text-center">
    <ArrowPathIcon class="w-8 h-8 mx-auto text-gray-400 animate-spin" />
    <p class="mt-2 text-sm text-gray-500">Loading patients...</p>
    <p v-if="isCacheError" class="mt-1 text-xs text-amber-600">
      Cache unavailable, fetching from server...
    </p>
  </div>
</div>
<div v-else-if="error && !patients.length" class="text-center p-8">
  <ExclamationTriangleIcon class="w-12 h-12 mx-auto text-red-400 mb-4" />
  <p class="text-red-600 font-medium">Error loading patients</p>
  <p class="text-sm text-gray-500 mt-2">{{ error.message }}</p>
  <button @click="refetch"
    class="mt-4 inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-stone-600 hover:bg-stone-700">
    <ArrowPathIcon class="mr-2 -ml-1 w-4 h-4" />
    Try Again
  </button>
</div>
<div v-else class="relative">
  <!-- Refresh indicator overlay when background refresh is happening -->
  <transition enter-active-class="transition ease-out duration-200"
    enter-from-class="opacity-0 translate-y-1"
    enter-to-class="opacity-100 translate-y-0"
    leave-active-class="transition ease-in duration-150"
    leave-from-class="opacity-100 translate-y-0"
    leave-to-class="opacity-0 translate-y-1">
    <div v-if="isRefreshing" class="absolute top-4 right-4 z-10">
      <div class="flex items-center gap-2 px-3 py-2 bg-blue-50 rounded-md shadow-sm">
        <ArrowPathIcon class="w-4 h-4 text-blue-600 animate-spin" />
        <span class="text-sm text-blue-600">Updating...</span>
      </div>
    </div>
  </transition>

  <!-- Error banner if network refresh failed but we have cached data -->
  <div v-if="error && patients.length" class="mb-4 p-3 bg-amber-50 border border-amber-200 rounded-md">
    <div class="flex">
      <ExclamationTriangleIcon class="h-5 w-5 text-amber-400" />
      <div class="ml-3">
        <p class="text-sm text-amber-800">
          Unable to refresh data. Showing cached results.
        </p>
      </div>
    </div>
  </div>

  <!-- Rest of the existing table content -->
</div>
```

### src/pages/MedicalIndexPage.vue

Replace lines 165-211:

```javascript
import { useCacheFirstQuery } from 'src/composables/use-cache-first-query'

const MEDICAL_QUERY = gql`
  query listMedicals($id: ID!) {
    medicals(organisationId: $id) {
      id
      status
      name
      medicalExpiryDate
      employment {
        companyName
      }
      patient {
        id
        fullName
        identificationNumber
        dob
      }
    }
  }
`;

const {
  result,
  loading,
  isRefreshing,
  error,
  refetch,
} = useCacheFirstQuery(MEDICAL_QUERY, { id: 1 })

const medicals = computed(() => {
  return result.value?.medicals || []
})

const medicalCount = computed(() => {
  return result.value?.medicals?.length || 0
})
```

### src/pages/CompanyIndexPage.vue

Replace lines 156-213:

```javascript
import { useCacheFirstQuery } from 'src/composables/use-cache-first-query'

const COMPANY_QUERY = gql`
  query listCompanies($id: ID!) {
    companies(organisationId: $id) {
      id
      name
      suburb
      industrySector
    }
  }
`;

const {
  result,
  loading,
  isRefreshing,
  error,
  refetch,
} = useCacheFirstQuery(COMPANY_QUERY, { id: 1 })

const companies = computed(() => {
  return result.value?.companies || []
})

const companyCount = computed(() => {
  return result.value?.companies?.length || 0
})
```

## ☐ **Phase 2: Enhance Loading States & Error Recovery**

### src/components/LoadingOverlay.vue

Create new file:

```vue
<template>
  <transition name="fade"
    enter-active-class="transition ease-out duration-200"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-active-class="transition ease-in duration-150"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0">
    <div v-if="visible" :class="overlayClasses">
      <div class="flex items-center gap-2">
        <ArrowPathIcon class="animate-spin" :class="iconClasses" />
        <span :class="textClasses">{{ message }}</span>
      </div>
    </div>
  </transition>
</template>

<script setup>
import { ArrowPathIcon } from '@heroicons/vue/24/outline'
import { computed } from 'vue'

const props = defineProps({
  visible: Boolean,
  message: { type: String, default: 'Loading...' },
  position: { type: String, default: 'center' }, // 'center' | 'top-right' | 'inline'
  variant: { type: String, default: 'primary' } // 'primary' | 'subtle'
})

const overlayClasses = computed(() => {
  const base = 'flex items-center justify-center'

  let positioning = ''
  switch(props.position) {
    case 'center':
      positioning = 'fixed inset-0 bg-white/80 z-50'
      break
    case 'top-right':
      positioning = 'absolute top-4 right-4 z-10'
      break
    case 'inline':
      positioning = 'p-8'
      break
  }

  const variant = props.variant === 'subtle'
    ? 'px-3 py-2 bg-blue-50 rounded-md shadow-sm'
    : ''

  return `${base} ${positioning} ${variant}`
})

const iconClasses = computed(() => {
  const size = props.position === 'center' ? 'w-8 h-8' : 'w-4 h-4'
  const color = props.variant === 'subtle' ? 'text-blue-600' : 'text-gray-400'
  return `${size} ${color}`
})

const textClasses = computed(() => {
  const size = props.position === 'center' ? 'text-base' : 'text-sm'
  const color = props.variant === 'subtle' ? 'text-blue-600' : 'text-gray-500'
  return `${size} ${color}`
})
</script>
```

### src/components/ErrorState.vue

Create new file:

```vue
<template>
  <div class="text-center p-8">
    <ExclamationTriangleIcon class="w-12 h-12 mx-auto text-red-400" />
    <h3 class="mt-2 text-sm font-medium text-gray-900">Error loading data</h3>
    <p class="mt-1 text-sm text-gray-500">{{ message }}</p>
    <p v-if="details" class="mt-2 text-xs text-gray-400">{{ details }}</p>
    <div class="mt-6">
      <button @click="$emit('retry')"
        class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-stone-600 hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500">
        <ArrowPathIcon class="mr-2 -ml-1 w-4 h-4" />
        Try Again
      </button>
    </div>
  </div>
</template>

<script setup>
import { ExclamationTriangleIcon, ArrowPathIcon } from '@heroicons/vue/24/outline'

defineProps({
  message: { type: String, default: 'An error occurred while loading data' },
  details: String
})

defineEmits(['retry'])
</script>
```

## ☐ **Phase 3: Add Cache Diagnostics & Monitoring**

### src/utils/apollo-cache-debug.js

Create new file:

```javascript
import localforage from 'localforage'

export async function getCacheStats(cache) {
  try {
    const extracted = cache.extract()
    const cacheSize = JSON.stringify(extracted).length
    const entityCount = Object.keys(extracted).length

    // Check LocalForage persistence
    const persistedData = await localforage.getItem('apollo-cache-persist')
    const isPersisted = !!persistedData
    const persistedSize = persistedData ? JSON.stringify(persistedData).length : 0

    // Group entities by type
    const entities = Object.keys(extracted).reduce((acc, key) => {
      const type = key.split(':')[0]
      acc[type] = (acc[type] || 0) + 1
      return acc
    }, {})

    // Sample some cached data for verification
    const samples = Object.entries(extracted)
      .slice(0, 3)
      .map(([key, value]) => ({
        key,
        hasData: !!value,
        fields: value ? Object.keys(value).length : 0
      }))

    return {
      cacheSize,
      cacheSizeKB: (cacheSize / 1024).toFixed(2),
      entityCount,
      isPersisted,
      persistedSize,
      persistedSizeKB: (persistedSize / 1024).toFixed(2),
      entities,
      samples,
      timestamp: new Date().toISOString()
    }
  } catch (error) {
    console.error('[Cache Stats] Failed to get cache statistics:', error)
    return {
      error: error.message,
      cacheSize: 0,
      entityCount: 0,
      isPersisted: false
    }
  }
}

export async function clearCache(cache) {
  try {
    console.log('[Cache] Clearing Apollo cache and LocalForage...')

    // Clear Apollo cache
    await cache.reset()

    // Clear LocalForage
    await localforage.removeItem('apollo-cache-persist')

    console.log('[Cache] Successfully cleared all cache data')
    return { success: true }
  } catch (error) {
    console.error('[Cache] Failed to clear cache:', error)
    return { success: false, error: error.message }
  }
}

export async function validateCachePersistence() {
  try {
    const testData = { test: true, timestamp: Date.now() }

    // Test write
    await localforage.setItem('apollo-test', testData)

    // Test read
    const retrieved = await localforage.getItem('apollo-test')

    // Validate
    const isValid = retrieved?.test === true && retrieved?.timestamp === testData.timestamp

    // Cleanup
    await localforage.removeItem('apollo-test')

    return { isValid, message: isValid ? 'Cache persistence working' : 'Cache persistence failed' }
  } catch (error) {
    return { isValid: false, message: error.message }
  }
}
```

### src/boot/apollo.js

Add after apolloClient creation (line 110):

```javascript
// Development cache monitoring and debugging
if (process.env.DEV) {
  // Expose cache for debugging
  window.__APOLLO_CACHE__ = cache
  window.__APOLLO_CLIENT__ = apolloClient

  // Add cache statistics function
  window.__APOLLO_CACHE_STATS__ = async () => {
    const { getCacheStats } = await import('src/utils/apollo-cache-debug')
    return getCacheStats(cache)
  }

  // Add cache clear function
  window.__APOLLO_CACHE_CLEAR__ = async () => {
    const { clearCache } = await import('src/utils/apollo-cache-debug')
    return clearCache(cache)
  }

  // Log cache operations
  const originalWrite = cache.write
  cache.write = function(...args) {
    console.log('[Cache Write]', args[0]?.result?.__typename || 'Unknown')
    return originalWrite.apply(this, args)
  }

  // Monitor cache size periodically
  setInterval(async () => {
    const stats = await window.__APOLLO_CACHE_STATS__()
    if (stats.entityCount > 1000) {
      console.warn(`[Cache] Large cache detected: ${stats.entityCount} entities, ${stats.cacheSizeKB}KB`)
    }
  }, 60000) // Check every minute
}
```

### src/pages/SettingsPage.vue

Add cache diagnostics section (append to template):

```vue
<div v-if="isDevelopment" class="mt-8">
  <div class="bg-white shadow rounded-lg">
    <div class="px-4 py-5 sm:p-6">
      <h3 class="text-lg leading-6 font-medium text-gray-900">
        Cache Diagnostics
      </h3>
      <div class="mt-2 max-w-xl text-sm text-gray-500">
        <p>Monitor and manage the Apollo GraphQL cache.</p>
      </div>

      <div v-if="cacheStats" class="mt-5 space-y-3">
        <div class="grid grid-cols-2 gap-4 text-sm">
          <div>
            <span class="text-gray-500">Cache Size:</span>
            <span class="ml-2 font-medium">{{ cacheStats.cacheSizeKB }} KB</span>
          </div>
          <div>
            <span class="text-gray-500">Entities:</span>
            <span class="ml-2 font-medium">{{ cacheStats.entityCount }}</span>
          </div>
          <div>
            <span class="text-gray-500">Persisted:</span>
            <span class="ml-2 font-medium">
              {{ cacheStats.isPersisted ? 'Yes' : 'No' }}
              <span v-if="cacheStats.isPersisted" class="text-xs text-gray-400">
                ({{ cacheStats.persistedSizeKB }} KB)
              </span>
            </span>
          </div>
          <div>
            <span class="text-gray-500">Last Check:</span>
            <span class="ml-2 font-medium text-xs">
              {{ new Date(cacheStats.timestamp).toLocaleTimeString() }}
            </span>
          </div>
        </div>

        <div v-if="cacheStats.entities" class="pt-3 border-t border-gray-200">
          <p class="text-sm font-medium text-gray-700 mb-2">Cached Types:</p>
          <div class="flex flex-wrap gap-2">
            <span v-for="(count, type) in cacheStats.entities" :key="type"
              class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {{ type }}: {{ count }}
            </span>
          </div>
        </div>
      </div>

      <div class="mt-5 flex gap-3">
        <button @click="refreshCacheStats"
          :disabled="loadingStats"
          class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500 disabled:opacity-50">
          <ArrowPathIcon class="mr-2 -ml-1 h-4 w-4" :class="{ 'animate-spin': loadingStats }" />
          Refresh Stats
        </button>

        <button @click="validatePersistence"
          class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500">
          Test Persistence
        </button>

        <button @click="clearCache"
          class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500">
          <TrashIcon class="mr-2 -ml-1 h-4 w-4" />
          Clear Cache
        </button>
      </div>

      <div v-if="cacheMessage"
        class="mt-4 p-3 rounded-md"
        :class="cacheMessageType === 'success' ? 'bg-green-50 text-green-800' : 'bg-amber-50 text-amber-800'">
        {{ cacheMessage }}
      </div>
    </div>
  </div>
</div>
```

Add to script section:

```javascript
import { ref, onMounted, computed } from 'vue'
import { getCacheStats, clearCache as clearCacheUtil, validateCachePersistence } from 'src/utils/apollo-cache-debug'
import { ArrowPathIcon, TrashIcon } from '@heroicons/vue/24/outline'
import { useApolloClient } from '@vue/apollo-composable'

const isDevelopment = computed(() => process.env.DEV)
const cacheStats = ref(null)
const loadingStats = ref(false)
const cacheMessage = ref('')
const cacheMessageType = ref('success')

const { client } = useApolloClient()

const refreshCacheStats = async () => {
  loadingStats.value = true
  try {
    cacheStats.value = await getCacheStats(client.cache)
  } catch (error) {
    console.error('Failed to get cache stats:', error)
    showMessage('Failed to get cache statistics', 'error')
  } finally {
    loadingStats.value = false
  }
}

const clearCache = async () => {
  if (confirm('Are you sure you want to clear all cached data? This action cannot be undone.')) {
    const result = await clearCacheUtil(client.cache)
    if (result.success) {
      showMessage('Cache cleared successfully', 'success')
      await refreshCacheStats()
    } else {
      showMessage(`Failed to clear cache: ${result.error}`, 'error')
    }
  }
}

const validatePersistence = async () => {
  const result = await validateCachePersistence()
  showMessage(result.message, result.isValid ? 'success' : 'error')
}

const showMessage = (message, type = 'success') => {
  cacheMessage.value = message
  cacheMessageType.value = type
  setTimeout(() => {
    cacheMessage.value = ''
  }, 5000)
}

onMounted(() => {
  if (isDevelopment.value) {
    refreshCacheStats()
  }
})
```
