{"editor.bracketPairColorization.enabled": true, "editor.guides.bracketPairs": true, "editor.formatOnSave": true, "editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": ["source.fixAll.eslint"], "eslint.validate": ["javascript", "javascriptreact", "typescript", "vue"], "json.schemas": [{"fileMatch": ["cypress.json"], "url": "https://on.cypress.io/cypress.schema.json"}, {"fileMatch": ["cypress.json"], "url": "https://on.cypress.io/cypress.schema.json"}], "[javascript]": {"editor.defaultFormatter": "vscode.typescript-language-features"}, "[vue]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}}