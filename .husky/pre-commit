#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🔍 Running pre-commit checks..."

# Run linting
echo "📝 Checking code style..."
npm run lint

# Run unit tests
echo "🧪 Running unit tests..."
npm run test:unit

# Check if tests passed
if [ $? -eq 0 ]; then
    echo "✅ All pre-commit checks passed!"
else
    echo "❌ Pre-commit checks failed. Please fix the issues before committing."
    exit 1
fi
