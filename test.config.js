/**
 * OccuSolve Test Configuration
 * Central configuration for all testing tools and automation
 */

module.exports = {
  // Test environment settings
  environment: {
    // Default test environment
    default: 'jsdom',
    
    // Test timeout settings
    timeouts: {
      unit: 10000,
      integration: 30000,
      e2e: 60000
    },
    
    // Test data settings
    testData: {
      // Use consistent test data across all tests
      useFixtures: true,
      fixturesPath: 'test/jest/fixtures',
      
      // Mock external services
      mockExternalServices: true,
      mockGraphQL: true
    }
  },

  // Coverage settings
  coverage: {
    // Coverage thresholds
    thresholds: {
      global: {
        branches: 0.2,
        functions: 0.7,
        lines: 17,
        statements: 17
      }
    },
    
    // Coverage reporting
    reporters: ['text', 'html', 'lcov', 'json'],
    
    // Files to include/exclude from coverage
    collectCoverageFrom: [
      'src/**/*.{js,vue}',
      '!src/**/*.spec.js',
      '!src/**/*.test.js',
      '!src/**/index.js',
      '!src/boot/*.js',
      '!src/router/routes.js'
    ],
    
    // Coverage output directory
    coverageDirectory: 'coverage',
    
    // Coverage report formats
    coverageReporters: ['text', 'html', 'lcov', 'json-summary']
  },

  // Unit test settings
  unit: {
    // Test file patterns
    testMatch: [
      '<rootDir>/test/jest/__tests__/**/*.spec.js'
    ],
    
    // Setup files
    setupFilesAfterEnv: [
      '<rootDir>/test/config/jest-environment-setup.js'
    ],
    
    // Module name mapping
    moduleNameMapping: {
      '^src/(.*)$': '<rootDir>/src/$1',
      '^test/(.*)$': '<rootDir>/test/$1'
    },
    
    // Transform settings
    transform: {
      '^.+\\.vue$': '@vue/vue3-jest',
      '^.+\\.js$': 'babel-jest'
    }
  },

  // Component test settings
  component: {
    // Cypress component testing configuration
    specPattern: 'test/cypress/component/**/*.spec.js',
    supportFile: 'test/cypress/support/component.js',
    
    // Component test environment
    devServer: {
      framework: 'vue',
      bundler: 'vite'
    }
  },

  // E2E test settings
  e2e: {
    // Cypress E2E configuration
    specPattern: 'test/cypress/integration/**/*.spec.js',
    supportFile: 'test/cypress/support/e2e.js',
    
    // Base URL for E2E tests
    baseUrl: 'http://localhost:9000',
    
    // Viewport settings
    viewportWidth: 1280,
    viewportHeight: 720,
    
    // Video and screenshot settings
    video: true,
    screenshotOnRunFailure: true,
    
    // Test isolation
    testIsolation: true
  },

  // Automation settings
  automation: {
    // Pre-commit hooks
    preCommit: {
      enabled: true,
      runLinting: true,
      runUnitTests: true,
      runCoverage: false
    },
    
    // CI/CD settings
    ci: {
      // Parallel test execution
      parallel: true,
      
      // Test retries
      retries: {
        unit: 0,
        component: 2,
        e2e: 2
      },
      
      // Artifact collection
      artifacts: {
        coverage: true,
        screenshots: true,
        videos: true,
        logs: true
      }
    },
    
    // Watch mode settings
    watch: {
      // Files to watch for changes
      watchPathIgnorePatterns: [
        'node_modules',
        'coverage',
        'dist',
        '.git'
      ],
      
      // Auto-run tests on file changes
      autoRun: true,
      
      // Notification settings
      notify: true,
      notifyMode: 'failure-change'
    }
  },

  // Reporting settings
  reporting: {
    // Test result formats
    formats: ['json', 'junit', 'html'],
    
    // Output directories
    outputDir: 'reports',
    
    // Report generation
    generateSummary: true,
    generateMetrics: true,
    generateTrends: false,
    
    // Integration with external services
    integrations: {
      codecov: {
        enabled: true,
        token: process.env.CODECOV_TOKEN
      },
      
      sonarqube: {
        enabled: false,
        projectKey: process.env.SONAR_PROJECT_KEY
      }
    }
  },

  // Performance settings
  performance: {
    // Test execution limits
    maxWorkers: '50%',
    
    // Memory settings
    maxMemory: '2GB',
    
    // Cache settings
    cache: true,
    cacheDirectory: 'node_modules/.cache/jest'
  },

  // Debug settings
  debug: {
    // Verbose output
    verbose: process.env.NODE_ENV === 'development',
    
    // Debug specific test types
    debugUnit: false,
    debugComponent: false,
    debugE2E: false,
    
    // Log levels
    logLevel: 'info'
  }
}
