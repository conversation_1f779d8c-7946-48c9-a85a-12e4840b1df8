#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Function to fix .or() syntax in Cypress test files
function fixCypressOrSyntax(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;

  // Pattern 1: .or('contains("text")') -> remove
  content = content.replace(/\.or\('contains\("[^"]*"\)'\)/g, '');
  
  // Pattern 2: .or("contains('text')") -> remove  
  content = content.replace(/\.or\("contains\('[^']*'\)"\)/g, '');
  
  // Pattern 3: .or('[selector]') -> remove
  content = content.replace(/\.or\('\[[^\]]*\]'\)/g, '');
  
  // Pattern 4: .or("selector") -> remove
  content = content.replace(/\.or\("[^"]*"\)/g, '');
  
  // Pattern 5: .or(cy.get(...)) -> remove entire .or() chain
  content = content.replace(/\.or\(cy\.get\([^)]*\)[^)]*\)/g, '');
  
  // Pattern 6: Multi-line .or() chains - remove lines that start with .or
  content = content.replace(/\n\s*\.or\([^)]*\)/g, '');
  
  // Pattern 7: .should(...).or(...) -> keep only .should(...)
  content = content.replace(/\.should\([^)]*\)\.or\([^)]*\)/g, (match) => {
    return match.split('.or(')[0];
  });

  if (content !== fs.readFileSync(filePath, 'utf8')) {
    modified = true;
    fs.writeFileSync(filePath, content);
    console.log(`Fixed: ${filePath}`);
  }
  
  return modified;
}

// Find all Cypress test files
function findCypressTestFiles(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        traverse(fullPath);
      } else if (item.endsWith('.spec.js') || item.endsWith('.test.js')) {
        files.push(fullPath);
      }
    }
  }
  
  traverse(dir);
  return files;
}

// Main execution
const testDir = 'test/cypress/integration';
if (fs.existsSync(testDir)) {
  const testFiles = findCypressTestFiles(testDir);
  
  console.log(`Found ${testFiles.length} test files`);
  
  let fixedCount = 0;
  for (const file of testFiles) {
    if (fixCypressOrSyntax(file)) {
      fixedCount++;
    }
  }
  
  console.log(`Fixed ${fixedCount} files`);
} else {
  console.log('Test directory not found');
}
