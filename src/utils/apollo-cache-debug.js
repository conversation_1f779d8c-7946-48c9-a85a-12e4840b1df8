import localforage from 'localforage'

/**
 * Get comprehensive cache statistics
 * @param {InMemoryCache} cache - Apollo cache instance
 * @returns {Object} Cache statistics
 */
export async function getCacheStats(cache) {
  try {
    const cacheData = cache.extract()
    const entities = Object.keys(cacheData)
    
    // Calculate cache size
    const cacheString = JSON.stringify(cacheData)
    const cacheSizeBytes = new Blob([cacheString]).size
    const cacheSizeKB = Math.round(cacheSizeBytes / 1024 * 100) / 100
    const cacheSizeMB = Math.round(cacheSizeKB / 1024 * 100) / 100
    
    // Analyze entity types
    const entityTypes = {}
    const entityCounts = {}
    
    entities.forEach(key => {
      if (key === 'ROOT_QUERY' || key === 'ROOT_MUTATION') {
        entityTypes[key] = (entityTypes[key] || 0) + 1
        return
      }
      
      // Extract type from key (e.g., "Patient:123" -> "Patient")
      const colonIndex = key.indexOf(':')
      if (colonIndex > 0) {
        const type = key.substring(0, colonIndex)
        entityTypes[type] = (entityTypes[type] || 0) + 1
        entityCounts[type] = entityCounts[type] || []
        entityCounts[type].push(key)
      } else {
        entityTypes['Unknown'] = (entityTypes['Unknown'] || 0) + 1
      }
    })
    
    // Get persistence info
    let persistedSize = 0
    let persistedData = null
    try {
      persistedData = await localforage.getItem('apollo-cache-persist')
      if (persistedData) {
        persistedSize = new Blob([JSON.stringify(persistedData)]).size
      }
    } catch (error) {
      console.warn('[Cache Debug] Failed to read persisted cache:', error)
    }
    
    // Calculate query statistics
    const rootQuery = cacheData.ROOT_QUERY || {}
    const queryCount = Object.keys(rootQuery).length
    
    return {
      timestamp: new Date().toISOString(),
      entityCount: entities.length,
      cacheSizeBytes,
      cacheSizeKB,
      cacheSizeMB,
      persistedSizeBytes: persistedSize,
      persistedSizeKB: Math.round(persistedSize / 1024 * 100) / 100,
      entityTypes,
      entityCounts,
      queryCount,
      hasPersistedData: !!persistedData,
      entities: entities.slice(0, 20), // First 20 entities for debugging
      sampleQueries: Object.keys(rootQuery).slice(0, 10), // First 10 queries
    }
  } catch (error) {
    console.error('[Cache Debug] Failed to get cache stats:', error)
    return {
      error: error.message,
      timestamp: new Date().toISOString(),
    }
  }
}

/**
 * Clear all cache data
 * @param {InMemoryCache} cache - Apollo cache instance
 * @returns {Promise<boolean>} Success status
 */
export async function clearCache(cache) {
  try {
    // Clear in-memory cache
    await cache.reset()
    
    // Clear persisted cache
    await localforage.removeItem('apollo-cache-persist')
    
    // Clear any other Apollo-related storage
    const keys = await localforage.keys()
    const apolloKeys = keys.filter(key => key.includes('apollo'))
    
    for (const key of apolloKeys) {
      await localforage.removeItem(key)
    }
    
    console.log('[Cache Debug] Cache cleared successfully')
    return true
  } catch (error) {
    console.error('[Cache Debug] Failed to clear cache:', error)
    return false
  }
}

/**
 * Validate cache persistence functionality
 * @returns {Promise<Object>} Validation result
 */
export async function validateCachePersistence() {
  const testKey = 'apollo-cache-persistence-test'
  const testData = {
    timestamp: Date.now(),
    test: 'persistence-validation',
    data: { nested: { value: 'test' } }
  }
  
  try {
    // Test write
    await localforage.setItem(testKey, testData)
    
    // Test read
    const retrieved = await localforage.getItem(testKey)
    
    // Validate data integrity
    const isValid = retrieved && 
                   retrieved.timestamp === testData.timestamp &&
                   retrieved.test === testData.test &&
                   retrieved.data.nested.value === testData.data.nested.value
    
    // Cleanup
    await localforage.removeItem(testKey)
    
    if (isValid) {
      return {
        success: true,
        message: 'Cache persistence working correctly',
        latency: Date.now() - testData.timestamp
      }
    } else {
      return {
        success: false,
        message: 'Data integrity check failed',
        expected: testData,
        received: retrieved
      }
    }
  } catch (error) {
    return {
      success: false,
      message: 'Persistence test failed',
      error: error.message
    }
  }
}

/**
 * Get cache performance metrics
 * @param {InMemoryCache} cache - Apollo cache instance
 * @returns {Object} Performance metrics
 */
export function getCachePerformanceMetrics(cache) {
  try {
    const cacheData = cache.extract()
    const startTime = performance.now()
    
    // Simulate cache operations
    const testQuery = Object.keys(cacheData.ROOT_QUERY || {})[0]
    if (testQuery) {
      cache.readQuery({
        query: { kind: 'Document', definitions: [] }, // Minimal query for testing
        variables: {}
      })
    }
    
    const readLatency = performance.now() - startTime
    
    return {
      readLatency: Math.round(readLatency * 100) / 100,
      entityCount: Object.keys(cacheData).length,
      memoryUsage: JSON.stringify(cacheData).length,
      timestamp: Date.now()
    }
  } catch (error) {
    return {
      error: error.message,
      timestamp: Date.now()
    }
  }
}

/**
 * Export cache data for debugging
 * @param {InMemoryCache} cache - Apollo cache instance
 * @returns {Object} Exportable cache data
 */
export function exportCacheData(cache) {
  try {
    const cacheData = cache.extract()
    return {
      timestamp: new Date().toISOString(),
      version: '1.0',
      cache: cacheData,
      metadata: {
        entityCount: Object.keys(cacheData).length,
        size: JSON.stringify(cacheData).length,
        exported: true
      }
    }
  } catch (error) {
    return {
      error: error.message,
      timestamp: new Date().toISOString()
    }
  }
}

/**
 * Monitor cache size and warn if it gets too large
 * @param {InMemoryCache} cache - Apollo cache instance
 * @param {number} maxSizeKB - Maximum size in KB before warning
 */
export function monitorCacheSize(cache, maxSizeKB = 5000) {
  try {
    const cacheData = cache.extract()
    const sizeBytes = new Blob([JSON.stringify(cacheData)]).size
    const sizeKB = sizeBytes / 1024
    
    if (sizeKB > maxSizeKB) {
      console.warn(`[Cache Monitor] Cache size (${Math.round(sizeKB)}KB) exceeds recommended limit (${maxSizeKB}KB)`)
      return {
        warning: true,
        currentSize: Math.round(sizeKB),
        maxSize: maxSizeKB,
        recommendation: 'Consider clearing old cache data or implementing cache eviction'
      }
    }
    
    return {
      warning: false,
      currentSize: Math.round(sizeKB),
      maxSize: maxSizeKB
    }
  } catch (error) {
    console.error('[Cache Monitor] Failed to monitor cache size:', error)
    return { error: error.message }
  }
}
