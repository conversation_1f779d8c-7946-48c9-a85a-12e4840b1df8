import { ref, computed, inject, provide, readonly } from 'vue'

// Global background loading state
const backgroundLoadingQueries = ref(new Set())
const backgroundLoadingCount = ref(0)

// Symbols for provide/inject
export const BACKGROUND_LOADING_KEY = Symbol('backgroundLoading')

/**
 * Global background loading state management
 * Tracks when GraphQL queries are performing background refreshes
 */
export function useBackgroundLoading() {
  // Check if we're in a component that has access to the provided state
  const providedState = inject(BACKGROUND_LOADING_KEY, null)
  
  if (providedState) {
    return providedState
  }

  // If not provided, create local state (for testing or standalone usage)
  const localQueries = ref(new Set())
  const localCount = ref(0)

  const addBackgroundQuery = (queryId) => {
    if (!localQueries.value.has(queryId)) {
      localQueries.value.add(queryId)
      localCount.value = localQueries.value.size
      
      if (process.env.DEV) {
        console.log(`[Background Loading] Added query: ${queryId}, total: ${localCount.value}`)
      }
    }
  }

  const removeBackgroundQuery = (queryId) => {
    if (localQueries.value.has(queryId)) {
      localQueries.value.delete(queryId)
      localCount.value = localQueries.value.size
      
      if (process.env.DEV) {
        console.log(`[Background Loading] Removed query: ${queryId}, total: ${localCount.value}`)
      }
    }
  }

  const isBackgroundLoading = computed(() => localCount.value > 0)

  return {
    addBackgroundQuery,
    removeBackgroundQuery,
    isBackgroundLoading: readonly(isBackgroundLoading),
    backgroundLoadingCount: readonly(localCount)
  }
}

/**
 * Provider function to be used at the app root level
 * This creates the global state that all components can access
 */
export function provideBackgroundLoading() {
  const queries = ref(new Set())
  const count = ref(0)

  const addBackgroundQuery = (queryId) => {
    if (!queries.value.has(queryId)) {
      queries.value.add(queryId)
      count.value = queries.value.size
      
      if (process.env.DEV) {
        console.log(`[Background Loading] Added query: ${queryId}, total: ${count.value}`)
      }
    }
  }

  const removeBackgroundQuery = (queryId) => {
    if (queries.value.has(queryId)) {
      queries.value.delete(queryId)
      count.value = queries.value.size
      
      if (process.env.DEV) {
        console.log(`[Background Loading] Removed query: ${queryId}, total: ${count.value}`)
      }
    }
  }

  const isBackgroundLoading = computed(() => count.value > 0)

  const state = {
    addBackgroundQuery,
    removeBackgroundQuery,
    isBackgroundLoading: readonly(isBackgroundLoading),
    backgroundLoadingCount: readonly(count)
  }

  provide(BACKGROUND_LOADING_KEY, state)

  return state
}

/**
 * Utility function to generate unique query IDs
 * Based on query string and variables
 */
export function generateQueryId(query, variables = {}) {
  const queryString = typeof query === 'string' ? query : query.loc?.source?.body || 'unknown'
  const variablesString = JSON.stringify(variables)
  return `${queryString.slice(0, 50)}-${btoa(variablesString).slice(0, 10)}`
}
