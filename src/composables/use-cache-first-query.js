import { useQuery } from '@vue/apollo-composable'
import { computed, ref, watchEffect, onMounted, onUnmounted, inject } from 'vue'
import { DefaultApolloClient } from '@vue/apollo-composable'
import { useBackgroundLoading, generateQueryId } from './use-background-loading'

export function useCacheFirstQuery(query, variables, options = {}) {
  const hasCachedData = ref(false)
  const cacheError = ref(null)

  // Get background loading state management
  const { addBackgroundQuery, removeBackgroundQuery } = useBackgroundLoading()

  // Generate unique ID for this query
  const queryId = generateQueryId(query, variables)

  // Start with cache-first to show cached data immediately
  const queryOptions = {
    ...options,
    fetchPolicy: 'cache-first',
    nextFetchPolicy: 'cache-and-network', // Background refresh after cache
    notifyOnNetworkStatusChange: true,
    errorPolicy: 'all', // Continue showing cached data even if network fails
  }

  const { result, loading, error, refetch, networkStatus } = useQuery(
    query,
    variables,
    queryOptions
  )

  // Get Apollo client for cache operations
  const apolloClient = inject(DefaultApolloClient)

  // Track background loading state
  watchEffect(() => {
    // NetworkStatus values: 1=loading, 2=setVariables, 3=fetchMore, 4=refetch, 6=poll, 7=ready, 8=error
    const isBackgroundRefresh = loading.value && hasCachedData.value && networkStatus.value === 4

    if (isBackgroundRefresh) {
      addBackgroundQuery(queryId)
    } else {
      removeBackgroundQuery(queryId)
    }
  })

  // Cleanup on unmount
  onUnmounted(() => {
    removeBackgroundQuery(queryId)
  })

  // Handle cache read failures with automatic network fallback
  onMounted(async () => {
    try {
      // Attempt to read from cache
      const cachedData = apolloClient.readQuery({
        query,
        variables,
      })

      if (cachedData) {
        hasCachedData.value = true
        console.log('[Cache] Found cached data for query')
      }
    } catch (err) {
      console.warn('[Cache] Failed to read from cache, falling back to network:', err)
      cacheError.value = err

      // Force network request if cache read fails
      try {
        await refetch()
      } catch (refetchError) {
        console.error('[Cache] Network fallback also failed:', refetchError)
      }
    }
  })

  // Track if we have cached data
  watchEffect(() => {
    if (result.value && !loading.value) {
      hasCachedData.value = true
      cacheError.value = null // Clear any cache errors once we have data
    }
  })

  // Compute loading states
  const isInitialLoad = computed(() => loading.value && !hasCachedData.value)
  const isRefreshing = computed(() => loading.value && hasCachedData.value)
  const isCacheError = computed(() => !!cacheError.value && !result.value)
  const isBackgroundRefresh = computed(() => loading.value && hasCachedData.value && networkStatus.value === 4)

  // Log cache status in development
  if (process.env.DEV) {
    watchEffect(() => {
      console.log('[Cache Query Status]', {
        queryId,
        hasCachedData: hasCachedData.value,
        isInitialLoad: isInitialLoad.value,
        isRefreshing: isRefreshing.value,
        isBackgroundRefresh: isBackgroundRefresh.value,
        networkStatus: networkStatus.value,
        hasError: !!error.value,
        cacheError: cacheError.value,
      })
    })
  }

  return {
    result,
    loading: isInitialLoad,
    isRefreshing,
    isBackgroundRefresh,
    isCacheError,
    error: computed(() => error.value || cacheError.value),
    refetch,
    hasCachedData,
    queryId,
  }
}
