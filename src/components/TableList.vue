<template>
  <PageHeading>
    <template v-slot:navigation>
      <div>
        <span class="text-xs text-gray-500"> &lt; </span>
        <span class="text-xs text-gray-500"> Back </span>
      </div>
    </template>
    <template v-slot:heading> Patients </template>
    <template v-slot:description> 24 Entries found </template>
    <template v-slot:buttons>
      <span class="">
        <button
          type="button"
          class="inline-flex items-center justify-center rounded-md border border-transparent bg-teal-700 px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-teal-700 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 sm:w-auto"
        >
          + Add new entry
        </button>
      </span>
    </template>
  </PageHeading>

  <div class="px-4 sm:px-6 lg:px-8">
    <div class="mt-8 flex flex-col">
      <div class="-my-2 -mx-4 overflow-x-auto sm:-mx-6 lg:-mx-8">
        <div
          class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8"
        >
          <table class="min-w-full divide-y divide-gray-300">
            <thead>
              <tr>
                <th
                  scope="col"
                  class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6 md:pl-0"
                >
                  Name
                </th>
                <th
                  scope="col"
                  class="py-3.5 px-3 text-left text-sm font-semibold text-gray-900"
                >
                  Title
                </th>
                <th
                  scope="col"
                  class="py-3.5 px-3 text-left text-sm font-semibold text-gray-900"
                >
                  Email
                </th>
                <th
                  scope="col"
                  class="py-3.5 px-3 text-left text-sm font-semibold text-gray-900"
                >
                  Role
                </th>
                <th
                  scope="col"
                  class="relative py-3.5 pl-3 pr-4 sm:pr-6 md:pr-0"
                >
                  <span class="sr-only">Edit</span>
                </th>
              </tr>
            </thead>
            <tbody class="divide-y divide-gray-200">
              <tr v-for="person in people" :key="person.email">
                <td
                  class="whitespace-nowrap py-4 pl-4 pr-3 text-sm font-medium text-gray-900 sm:pl-6 md:pl-0"
                >
                  {{ person.name }}
                </td>
                <td
                  class="whitespace-nowrap py-4 px-3 text-sm text-gray-500"
                >
                  {{ person.title }}
                </td>
                <td
                  class="whitespace-nowrap py-4 px-3 text-sm text-gray-500"
                >
                  {{ person.email }}
                </td>
                <td
                  class="whitespace-nowrap py-4 px-3 text-sm text-gray-500"
                >
                  {{ person.role }}
                </td>
                <td
                  class="relative whitespace-nowrap py-4 pl-3 pr-4 text-right text-sm font-medium sm:pr-6 md:pr-0"
                >
                  <a
                    href="#"
                    class="text-teal-600 hover:text-teal-900"
                    >Edit<span class="sr-only"
                      >, {{ person.name }}</span
                    ></a
                  >
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import PageHeading from "src/components/PageHeading.vue";
const people = [
  {
    name: "Lindsay Walton",
    title: "Front-end Developer",
    email: "<EMAIL>",
    role: "Member",
  },
  {
    name: "Lindsay Walton",
    title: "Front-end Developer",
    email: "<EMAIL>",
    role: "Member",
  },
  {
    name: "Lindsay Walton",
    title: "Front-end Developer",
    email: "<EMAIL>",
    role: "Member",
  },
  {
    name: "Lindsay Walton",
    title: "Front-end Developer",
    email: "<EMAIL>",
    role: "Member",
  },
];
export default {
  components: {
    PageHeading,
  },
  setup() {
    return {
      people,
    };
  },
};
</script>
