<template>
  <div
    class="overflow-y-auto flex-none vk-hidden w-28 bg-stone-100 md:block"
  >
    <div class="flex flex-col items-center w-full py-4">
      <div class="flex items-center flex-shrink-0">
        <img class="w-auto h-8" src="/logo.png" alt="Workflow" />
      </div>
      <div
        class="bg-teal-700 text-white px-2 py-0.5 text-center mt-4 w-full"
      >
        MarianMed
      </div>
      <div class="flex-1 w-full px-2 mt-8 space-y-1">
        <router-link
          to="/"
          custom
          v-slot="{ href, navigate, isExactActive }"
        >
          <a
            :href="href"
            @click="navigate"
            :class="[
              isExactActive
                ? 'bg-stone-800 text-white'
                : 'text-gray-500 hover:bg-stone-800 hover:text-white',
              'group w-full p-3 rounded-md flex flex-col items-center text-xs font-medium',
            ]"
            :aria-current="isExactActive ? 'page' : undefined"
          >
            <HomeIcon
              :class="[
                isExactActive
                  ? 'text-white'
                  : 'text-gray-700 group-hover:text-white',
                'h-6 w-6',
              ]"
              aria-hidden="true"
            />
            <span class="mt-2">Home</span>
          </a>
        </router-link>

        <router-link
          to="/patients"
          custom
          v-slot="{ href, navigate, isExactActive }"
        >
          <a
            :href="href"
            @click="navigate"
            :class="[
              isExactActive
                ? 'bg-stone-800 text-white'
                : 'text-gray-500 hover:bg-stone-800 hover:text-white',
              'group w-full p-3 rounded-md flex flex-col items-center text-xs font-medium',
            ]"
            :aria-current="isExactActive ? 'page' : undefined"
          >
            <Squares2X2Icon
              :class="[
                isExactActive
                  ? 'text-white'
                  : 'text-gray-700 group-hover:text-white',
                'h-6 w-6',
              ]"
              aria-hidden="true"
            />
            <span class="mt-2">Patients</span>
          </a>
        </router-link>

        <router-link
          to="/companies"
          custom
          v-slot="{ href, navigate, isExactActive }"
        >
          <a
            :href="href"
            @click="navigate"
            :class="[
              isExactActive
                ? 'bg-stone-800 text-white'
                : 'text-gray-500 hover:bg-stone-800 hover:text-white',
              'group w-full p-3 rounded-md flex flex-col items-center text-xs font-medium',
            ]"
            :aria-current="isExactActive ? 'page' : undefined"
          >
            <CubeIcon
              :class="[
                isExactActive
                  ? 'text-white'
                  : 'text-gray-700 group-hover:text-white',
                'h-6 w-6',
              ]"
              aria-hidden="true"
            />
            <span class="mt-2">Companies</span>
          </a>
        </router-link>

        <router-link
          to="/medicals"
          custom
          v-slot="{ href, navigate, isExactActive }"
        >
          <a
            :href="href"
            @click="navigate"
            :class="[
              isExactActive
                ? 'bg-stone-800 text-white'
                : 'text-gray-500 hover:bg-stone-800 hover:text-white',
              'group w-full p-3 rounded-md flex flex-col items-center text-xs font-medium',
            ]"
            :aria-current="isExactActive ? 'page' : undefined"
          >
            <RectangleStackIcon
              :class="[
                isExactActive
                  ? 'text-white'
                  : 'text-gray-700 group-hover:text-white',
                'h-6 w-6',
              ]"
              aria-hidden="true"
            />
            <span class="mt-2">Medicals</span>
          </a>
        </router-link>

        <router-link
          to="/settings"
          custom
          v-slot="{ href, navigate, isExactActive }"
        >
          <a
            :href="href"
            @click="navigate"
            :class="[
              isExactActive
                ? 'bg-stone-800 text-white'
                : 'text-gray-500 hover:bg-stone-800 hover:text-white',
              'group w-full p-3 rounded-md flex flex-col items-center text-xs font-medium',
            ]"
            :aria-current="isExactActive ? 'page' : undefined"
          >
            <CogIcon
              :class="[
                isExactActive
                  ? 'text-white'
                  : 'text-gray-700 group-hover:text-white',
                'h-6 w-6',
              ]"
              aria-hidden="true"
            />
            <span class="mt-2">Settings</span>
          </a>
        </router-link>
      </div>

      <!-- Offline Mode Indicator (Development Only) -->
      <div
        v-if="isDevelopment && isOfflineMode"
        class="mt-auto mb-4 mx-2"
      >
        <div
          class="bg-orange-100 border border-orange-300 rounded-lg p-2 text-center"
        >
          <ExclamationTriangleIcon
            class="h-5 w-5 text-orange-600 mx-auto mb-1"
          />
          <div class="text-xs font-medium text-orange-800">
            Offline Mode
          </div>
          <div class="text-xs text-orange-600">
            {{ formattedOfflineDuration }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from "vue";
import {
  CogIcon,
  Squares2X2Icon,
  RectangleStackIcon,
  CubeIcon,
  HomeIcon,
  ExclamationTriangleIcon,
} from "@heroicons/vue/24/outline";
import { useOfflineMode } from "src/composables/use-offline-mode";

// Development mode check
const isDevelopment = computed(() => process.env.DEV);

// Offline mode state
const { isOfflineMode, formattedOfflineDuration } = useOfflineMode();
</script>
