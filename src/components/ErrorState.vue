<template>
  <div class="error-state" :class="containerClasses">
    <div class="error-content">
      <!-- Error Icon -->
      <div class="error-icon" :class="iconClasses">
        <component :is="iconComponent" class="icon" />
      </div>
      
      <!-- Error Title -->
      <h3 class="error-title" :class="titleClasses">
        {{ title }}
      </h3>
      
      <!-- Error Message -->
      <p v-if="message" class="error-message" :class="messageClasses">
        {{ message }}
      </p>
      
      <!-- Error Details (collapsible) -->
      <div v-if="details && showDetails" class="error-details">
        <button 
          @click="toggleDetails" 
          class="details-toggle"
          :class="{ 'details-expanded': detailsExpanded }"
        >
          <ChevronDownIcon class="toggle-icon" />
          {{ detailsExpanded ? 'Hide' : 'Show' }} Details
        </button>
        
        <div v-if="detailsExpanded" class="details-content">
          <pre class="details-text">{{ details }}</pre>
        </div>
      </div>
      
      <!-- Action Buttons -->
      <div v-if="showActions" class="error-actions">
        <button 
          v-if="showRetry"
          @click="$emit('retry')"
          class="action-button action-button--primary"
          :disabled="retrying"
        >
          <ArrowPathIcon v-if="retrying" class="button-icon animate-spin" />
          <span>{{ retrying ? 'Retrying...' : 'Try Again' }}</span>
        </button>
        
        <button 
          v-if="showRefresh"
          @click="$emit('refresh')"
          class="action-button action-button--secondary"
        >
          <ArrowPathIcon class="button-icon" />
          <span>Refresh Page</span>
        </button>
        
        <button 
          v-if="showHome"
          @click="$emit('home')"
          class="action-button action-button--secondary"
        >
          <HomeIcon class="button-icon" />
          <span>Go Home</span>
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { 
  ExclamationTriangleIcon, 
  XCircleIcon, 
  WifiIcon,
  ArrowPathIcon,
  HomeIcon,
  ChevronDownIcon 
} from '@heroicons/vue/24/outline'

const props = defineProps({
  title: {
    type: String,
    default: 'Something went wrong'
  },
  message: {
    type: String,
    default: 'An unexpected error occurred. Please try again.'
  },
  details: {
    type: String,
    default: null
  },
  type: {
    type: String,
    default: 'error',
    validator: (value) => ['error', 'warning', 'network'].includes(value)
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  showRetry: {
    type: Boolean,
    default: true
  },
  showRefresh: {
    type: Boolean,
    default: false
  },
  showHome: {
    type: Boolean,
    default: false
  },
  showDetails: {
    type: Boolean,
    default: true
  },
  retrying: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['retry', 'refresh', 'home'])

const detailsExpanded = ref(false)

const toggleDetails = () => {
  detailsExpanded.value = !detailsExpanded.value
}

const iconComponent = computed(() => {
  switch (props.type) {
    case 'warning':
      return ExclamationTriangleIcon
    case 'network':
      return WifiIcon
    default:
      return XCircleIcon
  }
})

const containerClasses = computed(() => ({
  [`error-state--${props.size}`]: true,
  [`error-state--${props.type}`]: true
}))

const iconClasses = computed(() => ({
  [`error-icon--${props.type}`]: true,
  [`error-icon--${props.size}`]: true
}))

const titleClasses = computed(() => ({
  [`error-title--${props.size}`]: true
}))

const messageClasses = computed(() => ({
  [`error-message--${props.size}`]: true
}))

const showActions = computed(() => {
  return props.showRetry || props.showRefresh || props.showHome
})
</script>

<style scoped>
.error-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
}

.error-content {
  max-width: 400px;
  width: 100%;
}

/* Icon Styles */
.error-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 1rem;
}

.icon {
  flex-shrink: 0;
}

.error-icon--small .icon {
  width: 2rem;
  height: 2rem;
}

.error-icon--medium .icon {
  width: 3rem;
  height: 3rem;
}

.error-icon--large .icon {
  width: 4rem;
  height: 4rem;
}

.error-icon--error .icon {
  color: #dc2626;
}

.error-icon--warning .icon {
  color: #d97706;
}

.error-icon--network .icon {
  color: #6b7280;
}

/* Title Styles */
.error-title {
  margin: 0 0 0.5rem 0;
  font-weight: 600;
  color: #111827;
}

.error-title--small {
  font-size: 1rem;
}

.error-title--medium {
  font-size: 1.125rem;
}

.error-title--large {
  font-size: 1.25rem;
}

/* Message Styles */
.error-message {
  margin: 0 0 1.5rem 0;
  color: #6b7280;
  line-height: 1.5;
}

.error-message--small {
  font-size: 0.875rem;
}

.error-message--medium {
  font-size: 1rem;
}

.error-message--large {
  font-size: 1.125rem;
}

/* Details Styles */
.error-details {
  margin-bottom: 1.5rem;
}

.details-toggle {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: none;
  border: none;
  color: #6b7280;
  font-size: 0.875rem;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 0.25rem;
  transition: color 0.2s ease;
}

.details-toggle:hover {
  color: #374151;
}

.toggle-icon {
  width: 1rem;
  height: 1rem;
  transition: transform 0.2s ease;
}

.details-expanded .toggle-icon {
  transform: rotate(180deg);
}

.details-content {
  margin-top: 0.75rem;
  padding: 0.75rem;
  background-color: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  text-align: left;
}

.details-text {
  margin: 0;
  font-size: 0.75rem;
  color: #6b7280;
  white-space: pre-wrap;
  word-break: break-word;
}

/* Actions Styles */
.error-actions {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  align-items: center;
}

.action-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-weight: 500;
  font-size: 0.875rem;
  transition: all 0.2s ease;
  cursor: pointer;
  border: 1px solid transparent;
  min-width: 120px;
  justify-content: center;
}

.action-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-button--primary {
  background-color: #0d9488;
  color: white;
  border-color: #0d9488;
}

.action-button--primary:hover:not(:disabled) {
  background-color: #0f766e;
  border-color: #0f766e;
}

.action-button--secondary {
  background-color: white;
  color: #374151;
  border-color: #d1d5db;
}

.action-button--secondary:hover:not(:disabled) {
  background-color: #f9fafb;
  border-color: #9ca3af;
}

.button-icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
}

.animate-spin {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Size Variations */
.error-state--small {
  padding: 1rem;
}

.error-state--large {
  padding: 3rem;
}

/* Responsive */
@media (min-width: 640px) {
  .error-actions {
    flex-direction: row;
    justify-content: center;
  }
}
</style>
