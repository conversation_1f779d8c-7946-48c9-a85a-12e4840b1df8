<template>
  <div class="flex content-center justify-center">
    <div v-if="error">Error...</div>
    <div v-if="loading">Loading...</div>
    <div v-if="result && clinics && clinicCount == 0" class="w-full">
      Company Empty
    </div>
    <div
      v-else-if="result && clinics && clinicCount > 0"
      class="w-full"
    >
      <PageHeading class="pb-6 border-b border-stone-400">
        <template #heading> Clinic Management </template>

        <template #description>
          Listing of the registered clinic for this organisation
        </template>

        <template #buttons>
          <q-btn
            flat
            class="text-white bg-teal-700 text-bold"
            padding="md"
            no-caps
            to="/clinics/new"
          >
            <PlusSmallIcon class="w-5 h-5 mr-1 text-bold" />
            <div class="text-sm">Add</div>
          </q-btn>
        </template>
      </PageHeading>

      <div class="px-4 sm:px-6 lg:px-8">
        <div class="flex flex-col mt-8">
          <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div
              class="inline-block min-w-full py-2 align-middle md:px-1"
            >
              <div
                class="overflow-hidden shadow ring-0.5 ring-stone-50 ring-opacity-5 md:rounded-sm"
              >
                <table class="min-w-full divide-y divide-gray-100">
                  <thead class="">
                    <tr>
                      <th
                        scope="col"
                        class="py-3 pl-4 pr-3 text-xs font-medium tracking-wide text-left text-gray-500 uppercase sm:pl-6"
                      >
                        Clinic Name
                      </th>
                      <th
                        scope="col"
                        class="px-3 py-3 text-xs font-medium tracking-wide text-left text-gray-500 uppercase"
                      >
                        Address
                      </th>
                      <th
                        scope="col"
                        class="px-3 py-3 text-xs font-medium tracking-wide text-left text-gray-500 uppercase"
                      >
                        Industry
                      </th>
                      <th
                        scope="col"
                        class="relative py-3 pl-3 pr-4 sm:pr-6"
                      >
                        <span class="sr-only">Edit</span>
                      </th>
                    </tr>
                  </thead>
                  <tbody class="bg-white divide-y divide-gray-100">
                    <tr v-for="clinic in clinics" :key="clinic.id">
                      <td
                        class="py-4 pl-4 pr-3 text-sm font-medium whitespace-nowrap text-stone-900 sm:pl-6"
                      >
                        {{ clinic.clinicName }}
                      </td>
                      <td
                        class="px-3 py-4 text-sm whitespace-nowrap text-stone-500"
                      >
                        {{ clinic.physicalAddress }}
                        {{ clinic.physicalAddress2 }}
                      </td>
                      <td
                        class="px-3 py-4 text-sm whitespace-nowrap text-stone-500"
                      >
                        {{ clinic.phoneNumber }}
                      </td>
                      <td
                        class="relative flex py-4 pl-3 pr-4 text-sm font-medium text-left whitespace-nowrap sm:pr-6"
                      >
                        <router-link
                          :to="{
                            name: 'edit_clinic',
                            params: { id: clinic.id },
                          }"
                        >
                          <PencilIcon
                            class="w-5 h-5 text-stone-400 hover:text-teal-900"
                          />
                        </router-link>
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import {
  PlusIcon,
  PlusSmallIcon,
  PencilIcon,
} from "@heroicons/vue/24/outline";
import gql from "graphql-tag";
import { useCacheFirstQuery } from "src/composables/use-cache-first-query";
import { computed } from "vue";

const clinicCount = computed(() => {
  return result.value?.clinics?.length || 0;
});

const clinics = computed(() => {
  return result.value?.clinics || [];
});

const { result, error, loading } = useCacheFirstQuery(
  gql`
    query Clinics($id: ID!) {
      clinics(organisationId: $id) {
        id
        clinicName
        physicalAddress
        physicalAddress2
        phoneNumber
      }
    }
  `,
  {
    id: 1,
  },
);
</script>
