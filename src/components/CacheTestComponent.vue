<template>
  <div class="cache-test-component p-4 border rounded-lg">
    <h3 class="text-lg font-semibold mb-4">Cache-First Strategy Test</h3>
    
    <!-- Status indicators -->
    <div class="mb-4 space-y-2">
      <div class="flex items-center space-x-2">
        <span class="w-3 h-3 rounded-full" :class="loading ? 'bg-yellow-500' : 'bg-green-500'"></span>
        <span>Initial Loading: {{ loading ? 'Yes' : 'No' }}</span>
      </div>
      
      <div class="flex items-center space-x-2">
        <span class="w-3 h-3 rounded-full" :class="isRefreshing ? 'bg-blue-500' : 'bg-gray-300'"></span>
        <span>Background Refresh: {{ isRefreshing ? 'Yes' : 'No' }}</span>
      </div>
      
      <div class="flex items-center space-x-2">
        <span class="w-3 h-3 rounded-full" :class="hasCachedData ? 'bg-green-500' : 'bg-gray-300'"></span>
        <span>Has Cached Data: {{ hasCachedData ? 'Yes' : 'No' }}</span>
      </div>
      
      <div class="flex items-center space-x-2">
        <span class="w-3 h-3 rounded-full" :class="isBackgroundLoading ? 'bg-purple-500' : 'bg-gray-300'"></span>
        <span>Global Background Loading: {{ isBackgroundLoading ? 'Yes' : 'No' }}</span>
      </div>
    </div>
    
    <!-- Controls -->
    <div class="mb-4 space-x-2">
      <button 
        @click="refetch()"
        class="px-3 py-1 bg-blue-500 text-white rounded hover:bg-blue-600"
        :disabled="loading"
      >
        Refetch Data
      </button>
      
      <button 
        @click="simulateSlowQuery"
        class="px-3 py-1 bg-purple-500 text-white rounded hover:bg-purple-600"
        :disabled="simulatingSlowQuery"
      >
        {{ simulatingSlowQuery ? 'Simulating...' : 'Simulate Slow Query' }}
      </button>
    </div>
    
    <!-- Data display -->
    <div class="bg-gray-50 p-3 rounded">
      <h4 class="font-medium mb-2">Data ({{ patients.length }} patients):</h4>
      <div v-if="loading && !hasCachedData" class="text-gray-500">
        Loading initial data...
      </div>
      <div v-else-if="error" class="text-red-500">
        Error: {{ error.message }}
      </div>
      <div v-else class="space-y-1">
        <div v-for="patient in patients.slice(0, 3)" :key="patient.id" class="text-sm">
          {{ patient.fullName }} - {{ patient.phoneNumber }}
        </div>
        <div v-if="patients.length > 3" class="text-xs text-gray-500">
          ... and {{ patients.length - 3 }} more
        </div>
      </div>
    </div>
    
    <!-- Query info -->
    <div class="mt-4 text-xs text-gray-500">
      Query ID: {{ queryId?.slice(0, 20) }}...
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import gql from 'graphql-tag'
import { useCacheFirstQuery } from 'src/composables/use-cache-first-query'
import { useBackgroundLoading } from 'src/composables/use-background-loading'

const simulatingSlowQuery = ref(false)

// Test query
const PATIENTS_QUERY = gql`
  query listPatients($id: ID!) {
    patients(organisationId: $id) {
      id
      fullName
      phoneNumber
      identificationNumber
    }
  }
`

// Use cache-first query
const {
  result,
  loading,
  isRefreshing,
  isBackgroundRefresh,
  error,
  refetch,
  hasCachedData,
  queryId
} = useCacheFirstQuery(PATIENTS_QUERY, { id: 1 })

// Get global background loading state
const { isBackgroundLoading } = useBackgroundLoading()

// Computed data
const patients = computed(() => result.value?.patients || [])

// Simulate a slow query for testing
const simulateSlowQuery = async () => {
  simulatingSlowQuery.value = true
  try {
    // Add artificial delay to see the background loading indicator
    await new Promise(resolve => setTimeout(resolve, 2000))
    await refetch()
  } finally {
    simulatingSlowQuery.value = false
  }
}
</script>

<style scoped>
.cache-test-component {
  max-width: 500px;
}
</style>
