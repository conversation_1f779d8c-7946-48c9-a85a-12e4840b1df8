<template>
  <form @submit.prevent="submitForm">
    <PageHeading class="pb-6 border-b border-stone-400">
      <template #navigation>
        <div class="flex items-center text-gray-500 text-uppercase">
          <ArrowSmallLeftIcon class="w-5 h-5" />
          <router-link
            :to="{
              name: 'drug_screenings',
              params: { patient_id: route.params.patient_id },
            }"
            >Back</router-link
          >
        </div>
      </template>

      <template #heading> Capture a Drug Screening result </template>

      <template #description>
        Capture the detail of a new drug screening assessment
      </template>

      <template #buttons>
        <q-btn
          flat
          class="text-white bg-teal-700 text-bold"
          padding="md"
          no-caps
          :loading="sendFormLoading"
          type="submit"
        >
          <template v-slot:loading>
            <q-spinner-ios />
          </template>
          <CheckIcon class="w-5 h-5 mr-1 text-bold" />
          <div class="text-sm">Save</div>
        </q-btn>
      </template>
    </PageHeading>

    <div
      class="flex flex-col p-4 rounded-sm shadow md:mt-8 bg-stone-50"
    >
      <div class="max-w-4xl">
        <div class="grid grid-cols-2 gap-4">
          <div class="flex flex-col">
            <div
              class="mb-1 text-xs font-bold uppercase text-stone-400"
            >
              Patient fullName
            </div>
            <div class="font-bold">
              {{ patient.fullName }}
            </div>
          </div>
          <div class="flex flex-col">
            <div
              class="mb-1 text-xs font-bold uppercase text-stone-400"
            >
              ID Number
            </div>
            <div class="font-bold">
              {{ patient.identificationNumber }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-2xl">
      <div
        class="grid grid-cols-1 mt-6 gap-x-4 gap-y-6 sm:grid-cols-6"
      >
        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Result
          </h3>
        </div>
        <div class="sm:col-span-4">
          <div
            class="py-3 mt-2 ml-2 border-t first:mt-0 first:border-none sm:col-span-4"
          >
            <label class="text-base font-medium text-stone-500"
              >Cannabis (Urine)</label
            >
            <fieldset class="mt-3 ml-2">
              <legend class="sr-only">Cannabis Result</legend>
              <div
                class="space-y-4 sm:flex sm:items-center sm:space-y-0 sm:space-x-10"
              >
                <div class="flex items-center">
                  <input
                    id="cannabis"
                    name="cannabis"
                    type="radio"
                    value="Not Performed"
                    v-model="formData.cannabis"
                    class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                  />
                  <label
                    for="cannabis"
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Not Performed
                  </label>
                </div>

                <div class="flex items-center">
                  <input
                    id="cannabis-pos"
                    name="cannabis-pos"
                    type="radio"
                    value="Positive"
                    v-model="formData.cannabis"
                    class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                  />
                  <label
                    for="cannabis-pos"
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Positive
                  </label>
                </div>

                <div class="flex items-center">
                  <input
                    id="cannabis-neg"
                    name="cannabis-neg"
                    type="radio"
                    value="Negative"
                    v-model="formData.cannabis"
                    class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                  />
                  <label
                    for="cannabis-neg"
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Negative
                  </label>
                </div>
              </div>
            </fieldset>
          </div>

          <div
            class="py-3 mt-2 ml-2 border-t first:mt-0 first:border-none sm:col-span-4"
          >
            <label class="text-base font-medium text-stone-500"
              >Six Panel Drug Test</label
            >
            <fieldset class="mt-3 ml-2">
              <legend class="sr-only">Six Panel Result</legend>
              <div
                class="space-y-4 sm:flex sm:items-center sm:space-y-0 sm:space-x-10"
              >
                <div class="flex items-center">
                  <input
                    id="sixPanel"
                    name="sixPanel"
                    type="radio"
                    value="Not Performed"
                    v-model="formData.sixPanel"
                    class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                  />
                  <label
                    for="sixPanel"
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Not Performed
                  </label>
                </div>

                <div class="flex items-center">
                  <input
                    id="sixPanel-pos"
                    name="sixPanel-pos"
                    type="radio"
                    value="Positive"
                    v-model="formData.sixPanel"
                    class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                  />
                  <label
                    for="sixPanel-pos"
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Positive
                  </label>
                </div>

                <div class="flex items-center">
                  <input
                    id="sixPanel-neg"
                    name="sixPanel-neg"
                    type="radio"
                    value="Negative"
                    v-model="formData.sixPanel"
                    class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                  />
                  <label
                    for="sixPanel-neg"
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Negative
                  </label>
                </div>
              </div>
            </fieldset>
          </div>

          <div
            class="py-3 mt-2 ml-2 border-t first:mt-0 first:border-none sm:col-span-4"
          >
            <label class="text-base font-medium text-stone-500"
              >Gamma GT</label
            >
            <fieldset class="mt-3 ml-2">
              <legend class="sr-only">Gamma Result</legend>
              <div
                class="space-y-4 sm:flex sm:items-center sm:space-y-0 sm:space-x-10"
              >
                <div class="flex items-center">
                  <input
                    id="gamma"
                    name="gamma"
                    type="radio"
                    value="Not Performed"
                    v-model="formData.gamma"
                    class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                  />
                  <label
                    for="gamma"
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Not Performed
                  </label>
                </div>

                <div class="flex items-center">
                  <input
                    id="gamma-pos"
                    name="gamma-pos"
                    type="radio"
                    value="Positive"
                    v-model="formData.gamma"
                    class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                  />
                  <label
                    for="gamma-pos"
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Positive
                  </label>
                </div>

                <div class="flex items-center">
                  <input
                    id="gamma-neg"
                    name="gamma-neg"
                    type="radio"
                    value="Negative"
                    v-model="formData.gamma"
                    class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                  />
                  <label
                    for="gamma-neg"
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Negative
                  </label>
                </div>
              </div>
            </fieldset>
          </div>

          <div
            class="py-3 mt-2 ml-2 border-t first:mt-0 first:border-none sm:col-span-4"
          >
            <label class="text-base font-medium text-stone-500"
              >AST</label
            >
            <fieldset class="mt-3 ml-2">
              <legend class="sr-only">AST Result</legend>
              <div
                class="space-y-4 sm:flex sm:items-center sm:space-y-0 sm:space-x-10"
              >
                <div class="flex items-center">
                  <input
                    id="ast"
                    name="ast"
                    type="radio"
                    value="Not Performed"
                    v-model="formData.ast"
                    class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                  />
                  <label
                    for="ast"
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Not Performed
                  </label>
                </div>

                <div class="flex items-center">
                  <input
                    id="ast-pos"
                    name="ast-pos"
                    type="radio"
                    value="Positive"
                    v-model="formData.ast"
                    class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                  />
                  <label
                    for="ast-pos"
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Positive
                  </label>
                </div>

                <div class="flex items-center">
                  <input
                    id="ast-neg"
                    name="ast-neg"
                    type="radio"
                    value="Negative"
                    v-model="formData.ast"
                    class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                  />
                  <label
                    for="ast-neg"
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Negative
                  </label>
                </div>
              </div>
            </fieldset>
          </div>

          <div
            class="py-3 mt-2 ml-2 border-t first:mt-0 first:border-none sm:col-span-4"
          >
            <label class="text-base font-medium text-stone-500"
              >FBC</label
            >
            <fieldset class="mt-3 ml-2">
              <legend class="sr-only">FBC Result</legend>
              <div
                class="space-y-4 sm:flex sm:items-center sm:space-y-0 sm:space-x-10"
              >
                <div class="flex items-center">
                  <input
                    id="fbc"
                    name="fbc"
                    type="radio"
                    value="Not Performed"
                    v-model="formData.fbc"
                    class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                  />
                  <label
                    for="fbc"
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Not Performed
                  </label>
                </div>

                <div class="flex items-center">
                  <input
                    id="fbc-pos"
                    name="fbc-pos"
                    type="radio"
                    value="Positive"
                    v-model="formData.fbc"
                    class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                  />
                  <label
                    for="fbc-pos"
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Positive
                  </label>
                </div>

                <div class="flex items-center">
                  <input
                    id="fbc-neg"
                    name="fbc-neg"
                    type="radio"
                    value="Negative"
                    v-model="formData.fbc"
                    class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                  />
                  <label
                    for="fbc-neg"
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Negative
                  </label>
                </div>
              </div>
            </fieldset>
          </div>

          <div
            class="py-3 mt-2 ml-2 border-t first:mt-0 first:border-none sm:col-span-4"
          >
            <label class="text-base font-medium text-stone-500"
              >HIV</label
            >
            <fieldset class="mt-3 ml-2">
              <legend class="sr-only">hiv Result</legend>
              <div
                class="space-y-4 sm:flex sm:items-center sm:space-y-0 sm:space-x-10"
              >
                <div class="flex items-center">
                  <input
                    id="hiv"
                    name="hiv"
                    type="radio"
                    value="Not Performed"
                    v-model="formData.hiv"
                    class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                  />
                  <label
                    for="hiv"
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Not Performed
                  </label>
                </div>

                <div class="flex items-center">
                  <input
                    id="hiv-pos"
                    name="hiv-pos"
                    type="radio"
                    value="Positive"
                    v-model="formData.hiv"
                    class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                  />
                  <label
                    for="hiv-pos"
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Positive
                  </label>
                </div>

                <div class="flex items-center">
                  <input
                    id="hiv-neg"
                    name="hiv-neg"
                    type="radio"
                    value="Negative"
                    v-model="formData.hiv"
                    class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                  />
                  <label
                    for="hiv-neg"
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Negative
                  </label>
                </div>
              </div>
            </fieldset>
          </div>
        </div>

        <div class="mt-4 sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Further Information
          </h3>
        </div>

        <div class="sm:col-span-4">
          <v-date-picker
            color="teal"
            v-model="formData.datePerformed"
            :masks="masks"
            class=""
          >
            <template v-slot="{ inputValue, togglePopover }">
              <label
                for="employment_start_date"
                class="block mb-1 text-sm font-medium text-stone-400"
                >Date Performed</label
              >
              <input
                class="w-full px-2 py-2 font-semibold border rounded-sm shadow-sm border-stone-300 focus:border-stone-500 focus:outline-none focus:ring-1 focus:ring-stone-500"
                :value="inputValue"
                @click="togglePopover"
              />
            </template>
          </v-date-picker>
          <div
            class=""
            v-for="error of v$.datePerformed.$errors"
            :key="error.$uid"
          >
            <div class="text-red-700">{{ error.$message }}</div>
          </div>
        </div>

        <div class="sm:col-span-3">
          <label
            for="performedBy"
            class="block text-sm font-medium text-stone-400"
          >
            Performed by *</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              v-model="formData.performedBy"
              name="performedBy"
              id="performedBy"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
          </div>
          <div
            class=""
            v-for="error of v$.performedBy.$errors"
            :key="error.$uid"
          >
            <div class="text-red-700">{{ error.$message }}</div>
          </div>
        </div>

        <div class="sm:col-span-3">
          <div class="mt-1">
            <Listbox as="div" v-model="selectedClinic">
              <ListboxLabel
                class="block text-sm font-medium text-stone-400"
              >
                Clinic Attended*
              </ListboxLabel>
              <div class="relative mt-1">
                <ListboxButton
                  class="relative w-full py-2 pl-3 pr-10 text-left bg-white border border-gray-300 rounded-sm shadow-sm cursor-default focus:outline-none focus:ring-1 focus:ring-stone-500 focus:border-stone-300 sm:text-sm"
                >
                  <span class="inline-flex w-full truncate">
                    <span class="truncate">{{
                      selectedClinic.clinicName
                    }}</span></span
                  >
                  <span
                    class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"
                  >
                    <ChevronUpDownIcon
                      class="w-5 h-5 text-gray-400"
                      aria-hidden="true"
                    />
                  </span>
                </ListboxButton>

                <transition
                  leave-active-class="transition duration-100 ease-in"
                  leave-from-class="opacity-100"
                  leave-to-class="opacity-0"
                >
                  <ListboxOptions
                    class="absolute z-10 w-full py-1 mt-1 overflow-auto text-base bg-white rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
                  >
                    <ListboxOption
                      as="template"
                      v-for="clinic in clinics"
                      :key="clinic.id"
                      :value="clinic"
                      v-slot="{ active, selected }"
                    >
                      <li
                        :class="[
                          active
                            ? 'text-white bg-stone-600'
                            : 'text-gray-900',
                          'cursor-default select-none relative py-2 pl-3 pr-9',
                        ]"
                      >
                        <div class="flex">
                          <span
                            :class="[
                              selected
                                ? 'font-semibold'
                                : 'font-normal',
                              'truncate',
                            ]"
                          >
                            {{ clinic.clinicName }}
                          </span>
                          <span
                            :class="[
                              active
                                ? 'text-stone-200'
                                : 'text-gray-500',
                              'ml-2 truncate',
                            ]"
                          >
                          </span>
                        </div>

                        <span
                          v-if="selected"
                          :class="[
                            active ? 'text-white' : 'text-stone-600',
                            'absolute inset-y-0 right-0 flex items-center pr-4',
                          ]"
                        >
                          <CheckIcon
                            class="w-5 h-5"
                            aria-hidden="true"
                          />
                        </span>
                      </li>
                    </ListboxOption>
                  </ListboxOptions>
                </transition>
              </div>
            </Listbox>
            <div
              class=""
              v-for="error of v$.clinicId.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="identification"
            class="block text-sm font-medium text-stone-400"
          >
            Comment</label
          >
          <div class="mt-1 mb-4">
            <textarea
              autocomplete="new_result"
              v-model="formData.comment"
              rows="4"
              type="text"
              name="identification"
              id="identification"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
          </div>
        </div>
      </div>
    </div>
  </form>
</template>

<script setup>
import {
  ArrowSmallLeftIcon,
  CheckIcon,
  ChevronUpDownIcon,
} from "@heroicons/vue/24/outline";
import gql from "graphql-tag";
import { useQuery, useMutation } from "@vue/apollo-composable";
import { useRoute, useRouter } from "vue-router";
import { ref, reactive, watch, computed } from "vue";
import useVuelidate from "@vuelidate/core";
import { required, helpers } from "@vuelidate/validators";

const route = useRoute();

const masks = {
  input: "YYYY/MM/DD",
  mode: "date",
  data: ["L", "YYYY-MM-DD", "YYYY/MM/DD"],
};

const GET_PATIENT = gql`
  query getPatient($id: ID!) {
    patient(id: $id) {
      id
      fullName
      identificationNumber
    }
  }
`;

const { result: patientResult } = useQuery(
  GET_PATIENT,
  { id: route.params.patient_id },
  {
    fetchPolicy: "cache-and-network",
  },
);

const CREATE_LAB_TEST = gql`
  mutation createLabTest($input: LabTestInput!) {
    createLabTest(input: $input) {
      success
      labTest {
        reportName
        comment
        id
      }
    }
  }
`;

const {
  mutate: sendForm,
  onDone,
  loading: sendFormLoading,
} = useMutation(CREATE_LAB_TEST, () => ({
  variables: {
    id: route.params.patient_id,
    input: {
      ...formData,
    },
  },
}));

const patient = computed(() => {
  return patientResult.value?.patient ?? "Patient";
});

const testResult = [
  { id: "none", title: "Not Performed" },
  { id: "positive", title: "Positive" },
  { id: "negative", title: "Negative " },
];

const formData = reactive({
  cannabis: testResult[0].title,
  sixPanel: testResult[0].title,
  gamma: testResult[0].title,
  ast: testResult[0].title,
  fbc: testResult[0].title,
  hiv: testResult[0].title,
  patientId: parseInt(route.params.patient_id),
  clinicId: "",
  performedBy: "",
  comment: "",
  datePerformed: "",
});

const selectedClinic = ref("");

watch(
  () => selectedClinic.value,
  (outcome) => {
    formData.clinicId = parseInt(outcome.id);
  },
);

const CLINICS = gql`
  query listClinics($id: ID!) {
    clinics(organisationId: $id) {
      id
      clinicName
    }
  }
`;

const { result: clinicResult } = useQuery(
  CLINICS,
  { id: 1 },
  { fetchPolicy: "cache-and-network" },
);
const rules = {
  datePerformed: {
    required: helpers.withMessage(
      "Date performed is required.",
      required,
    ),
  },
  performedBy: {
    required: helpers.withMessage(
      "Performed by is required.",
      required,
    ),
  },

  clinicId: {
    required: helpers.withMessage(
      "Clinic selection is required.",
      required,
    ),
  },
};

const clinics = computed(() => clinicResult?.value.clinics ?? []);

const v$ = useVuelidate(rules, formData);

const router = useRouter();

const submitForm = async () => {
  const isFormCorrect = await v$.value.$validate();
  if (!isFormCorrect) return;
  sendForm();
};

onDone((result) => {
  if (result.data.createLabTest.success == true) {
    router.push({
      name: "drug_screenings",
      params: { id: route.params.patient_id },
    });
  } else console.log("From Error:", result?.data);
});
</script>
