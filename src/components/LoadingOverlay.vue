<template>
  <div class="loading-overlay" :class="overlayClasses">
    <div class="loading-content">
      <!-- Spinner -->
      <div class="loading-spinner" :class="spinnerClasses">
        <div class="spinner-ring"></div>
      </div>
      
      <!-- Loading text -->
      <p v-if="message" class="loading-message" :class="messageClasses">
        {{ message }}
      </p>
      
      <!-- Progress indicator (optional) -->
      <div v-if="showProgress && progress !== null" class="loading-progress">
        <div class="progress-bar">
          <div 
            class="progress-fill" 
            :style="{ width: `${progress}%` }"
          ></div>
        </div>
        <span class="progress-text">{{ progress }}%</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  message: {
    type: String,
    default: 'Loading...'
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary', 'light'].includes(value)
  },
  overlay: {
    type: Boolean,
    default: false
  },
  progress: {
    type: Number,
    default: null,
    validator: (value) => value === null || (value >= 0 && value <= 100)
  },
  showProgress: {
    type: Boolean,
    default: false
  }
})

const overlayClasses = computed(() => ({
  'loading-overlay--overlay': props.overlay,
  [`loading-overlay--${props.variant}`]: true
}))

const spinnerClasses = computed(() => ({
  [`loading-spinner--${props.size}`]: true,
  [`loading-spinner--${props.variant}`]: true
}))

const messageClasses = computed(() => ({
  [`loading-message--${props.size}`]: true,
  [`loading-message--${props.variant}`]: true
}))
</script>

<style scoped>
.loading-overlay {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.loading-overlay--overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(2px);
  z-index: 1000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

/* Spinner Styles */
.loading-spinner {
  position: relative;
}

.spinner-ring {
  border-radius: 50%;
  border-style: solid;
  animation: spin 1s linear infinite;
}

/* Spinner Sizes */
.loading-spinner--small .spinner-ring {
  width: 1.5rem;
  height: 1.5rem;
  border-width: 2px;
}

.loading-spinner--medium .spinner-ring {
  width: 2rem;
  height: 2rem;
  border-width: 3px;
}

.loading-spinner--large .spinner-ring {
  width: 3rem;
  height: 3rem;
  border-width: 4px;
}

/* Spinner Variants */
.loading-spinner--primary .spinner-ring {
  border-color: #0d9488 transparent #0d9488 transparent;
}

.loading-spinner--secondary .spinner-ring {
  border-color: #6b7280 transparent #6b7280 transparent;
}

.loading-spinner--light .spinner-ring {
  border-color: #ffffff transparent #ffffff transparent;
}

/* Message Styles */
.loading-message {
  margin: 0;
  font-weight: 500;
  text-align: center;
}

.loading-message--small {
  font-size: 0.875rem;
}

.loading-message--medium {
  font-size: 1rem;
}

.loading-message--large {
  font-size: 1.125rem;
}

.loading-message--primary {
  color: #374151;
}

.loading-message--secondary {
  color: #6b7280;
}

.loading-message--light {
  color: #ffffff;
}

/* Progress Styles */
.loading-progress {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  max-width: 200px;
}

.progress-bar {
  width: 100%;
  height: 4px;
  background-color: #e5e7eb;
  border-radius: 2px;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background-color: #0d9488;
  border-radius: 2px;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

/* Animation */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Fade transition */
.loading-overlay {
  transition: opacity 0.2s ease;
}
</style>
