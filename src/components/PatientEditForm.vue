<template>
  <div>PatientID:</div>
</template>

<script setup>
import { useRoute } from "vue-router";
import { useQuery } from "@vue/apollo-composable";
import { watch } from "vue";

const route = useRoute();

const PATIENT = gql`
  query getPatient($id: ID!) {
    patient(id: $id) {
      firstName
      lastName
      dob
      gender
      identificationNumber
      phoneNumber
      email
    }
  }
`;
</script>
