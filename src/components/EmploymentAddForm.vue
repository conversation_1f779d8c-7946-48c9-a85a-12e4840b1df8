<!-- This example requires Tailwind CSS v2.0+ -->
<template>
  <TransitionRoot as="template" :show="open">
    <Dialog
      as="div"
      class="fixed z-10 inset-0 overflow-y-auto"
      @close="open = false"
    >
      <div
        class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0"
      >
        <TransitionChild
          as="template"
          enter="ease-out duration-300"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="ease-in duration-200"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <DialogOverlay
            class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity"
          />
        </TransitionChild>

        <!-- This element is to trick the browser into centering the modal contents. -->
        <span
          class="vk-hidden sm:inline-block sm:align-middle sm:h-screen"
          aria-hidden="true"
          >&#8203;
        </span>
        <TransitionChild
          as="template"
          enter="ease-out duration-300"
          enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          enter-to="opacity-100 translate-y-0 sm:scale-100"
          leave="ease-in duration-200"
          leave-from="opacity-100 translate-y-0 sm:scale-100"
          leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
        >
          <div
            class="relative inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-xl sm:w-full"
          >
            <div
              class="vk-hidden sm:block absolute top-0 right-0 pt-4 pr-4"
            >
              <button
                type="button"
                class="bg-transparent rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500"
                @click="open = false"
              >
                <span class="sr-only">Close</span>
                <XIcon class="h-6 w-6" aria-hidden="true" />
              </button>
            </div>
            <div class="sm:items-start">
              <div class="text-center sm:mt-0 sm:text-left">
                <div
                  class="bg-white overflow-hidden shadow rounded-lg"
                >
                  <div class="bg-stone-100 px-4 py-4 sm:px-6">
                    <DialogTitle
                      as="div"
                      class="text-md leading-6 font-medium text-gray-900"
                    >
                      Capture Employment information
                    </DialogTitle>
                  </div>

                  <div class="px-4 py-5 sm:p-6">
                    <div>
                      <h3
                        class="text-lg leading-6 font-semibold text-gray-900"
                      >
                        Company
                      </h3>
                    </div>
                    <div class="mt-4">
                      <label
                        for="company_name"
                        class="block text-sm font-medium text-gray-700"
                        >Name</label
                      >
                      <div class="mt-1">
                        <input
                          type="text"
                          name="company_name"
                          id="company_name"
                          class="shadow-sm focus:ring-stone-500 focus:border-stone-500 block w-full sm:text-sm border-gray-300 rounded-sm"
                          placeholder=""
                        />
                      </div>
                    </div>
                    <div class="mt-6">
                      <h3
                        class="text-lg leading-6 font-semibold text-gray-900"
                      >
                        Employment
                      </h3>
                    </div>

                    <div class="mt-4">
                      <label
                        for="employment_department"
                        class="block text-sm font-medium text-stone-400"
                        >Department</label
                      >
                      <div class="mt-1">
                        <input
                          type="text"
                          name="employment_position"
                          id="employment_department"
                          class="shadow-sm focus:ring-stone-500 focus:border-stone-500 block w-full sm:text-sm border-gray-300 rounded-sm"
                          placeholder=""
                        />
                      </div>
                    </div>
                    <div class="mt-4 max-w-xs">
                      <label
                        for="employment_position"
                        class="block text-sm font-medium text-stone-400"
                        >Position</label
                      >
                      <div class="mt-1">
                        <input
                          type="text"
                          name="employment_position"
                          id="employment_position"
                          class="shadow-sm focus:ring-stone-500 focus:border-stone-500 block w-full sm:text-sm border-gray-300 rounded-sm"
                          placeholder=""
                        />
                      </div>
                    </div>
                    <div class="mt-4 max-w-xs">
                      <v-date-picker
                        color="teal"
                        v-model="date"
                        :masks="masks"
                        class=""
                      >
                        <template
                          v-slot="{ inputValue, inputEvents }"
                        >
                          <label
                            for="employment_start_date"
                            class="block mb-1 text-sm font-medium text-stone-400"
                            >Start of Employment</label
                          >
                          <input
                            class="w-full shadow-sm px-2 py-2 border-stone-300 focus:border-stone-500 border focus:outline-none focus:ring-1 focus:ring-stone-500 font-semibold rounded-sm"
                            :value="inputValue"
                            v-on="inputEvents"
                          />
                        </template>
                      </v-date-picker>
                    </div>
                  </div>
                  <div
                    class="flex justify-between bg-stone-100 px-4 py-4 sm:px-6"
                  >
                    <button
                      type="button"
                      class="bg-white py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500"
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      class="ml-3 inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-teal-700 hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-stone-500"
                    >
                      Save
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </TransitionChild>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script>
import { ref } from "vue";
import {
  Dialog,
  DialogOverlay,
  DialogTitle,
  TransitionChild,
  TransitionRoot,
} from "@headlessui/vue";
import { XIcon } from "@heroicons/vue/outline";

export default {
  components: {
    Dialog,
    DialogOverlay,
    DialogTitle,
    TransitionChild,
    TransitionRoot,
    XIcon,
  },
  setup() {
    const open = ref(true);

    return {
      open,
      date: "",
      masks: {
        input: "YYYY/MM/DD",
      },
    };
  },
};
</script>
