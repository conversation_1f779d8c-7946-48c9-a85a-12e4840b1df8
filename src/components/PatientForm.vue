<template>
  <form @submit.prevent="submitForm">
    <PageHeading class="pb-6 border-b border-stone-400">
      <template #navigation>
        <div class="flex items-center text-gray-500 text-uppercase">
          <ArrowSmallLeftIcon class="w-5 h-5" />
          <router-link to="/patients">Back</router-link>
        </div>
      </template>

      <template #heading> Create a Patient record </template>

      <template #description>
        Capture the details of a new patient
      </template>

      <template #buttons>
        <q-btn
          flat
          class="text-white bg-teal-700 text-bold"
          padding="md"
          no-caps
          :loading="sendFormLoading"
          type="submit"
        >
          <template v-slot:loading>
            <q-spinner-ios />
          </template>
          <CheckIcon class="w-5 h-5 mr-1 text-bold" />
          <div class="text-sm">Save</div>
        </q-btn>
      </template>
    </PageHeading>

    <div class="max-w-2xl">
      <div
        class="grid grid-cols-1 mt-6 gap-x-4 gap-y-6 sm:grid-cols-6"
      >
        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Personal Information
          </h3>
        </div>

        <div class="sm:col-span-4">
          <label
            for="identification"
            class="block text-sm font-medium text-stone-400"
          >
            Identification Number*</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              v-model="formData.identificationNumber"
              @blur="v$.identificationNumber.$touch"
              name="identification"
              id="identification"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <div
              class=""
              v-for="error of v$.identificationNumber.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-3">
          <label
            for="firstName"
            class="block text-sm font-medium text-stone-400"
            >First Name*</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="firstName"
              id="firstName"
              v-model="formData.firstName"
              @blur="v$.firstName.$touch"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <div
              class=""
              v-for="error of v$.firstName.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-3">
          <label
            for="lastName"
            class="block text-sm font-medium text-stone-400"
            >Last Name*</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="lastName"
              id="lastName"
              v-model="formData.lastName"
              @blur="v$.lastName.$touch"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800 placeholder:text-slate-300 placeholder:font-normal"
              placeholder=""
            />
          </div>
          <div
            class=""
            v-for="error of v$.lastName.$errors"
            :key="error.$uid"
          >
            <div class="text-red-700">{{ error.$message }}</div>
          </div>
        </div>

        <div class="sm:col-span-4">
          <v-date-picker
            color="teal"
            v-model="formData.dob"
            mode="date"
            :masks="masks"
          >
            <template v-slot="{ inputValue, inputEvents }">
              <label
                for="identification"
                class="block mb-1 text-sm font-medium text-stone-400"
              >
                Date of Birth*</label
              >
              <input
                class="w-full px-2 py-2 font-semibold border rounded-sm shadow-sm border-stone-300 focus:ring-stone-500 focus:border-stone-300 focus:outline-none"
                :value="inputValue"
                @blur="v$.dob.$touch"
                v-on="inputEvents"
              />
            </template>
          </v-date-picker>
          <div
            class=""
            v-for="error of v$.dob.$errors"
            :key="error.$uid"
          >
            <div class="text-red-700">{{ error.$message }}</div>
          </div>
        </div>

        <div class="sm:col-span-4">
          <div class="flex items-center justify-between">
            <h2 class="text-sm font-medium text-stone-400">
              Gender*
            </h2>
          </div>

          <RadioGroup v-model="formData.gender" class="mt-2">
            <RadioGroupLabel class="sr-only">
              Choose a gender
            </RadioGroupLabel>
            <div class="grid grid-cols-3 gap-3 sm:grid-cols-6">
              <RadioGroupOption
                as="template"
                v-for="option in memoryOptions"
                :key="option.name"
                :value="option.name"
                v-slot="{ active, checked }"
              >
                <div
                  :class="[
                    active
                      ? 'ring-2 ring-offset-2 ring-teal-500'
                      : '',
                    checked
                      ? 'bg-teal-600 border-transparent text-white hover:bg-teal-700'
                      : 'bg-white border-gray-200 text-gray-900 hover:bg-gray-50',
                    'cursor-pointer focus:outline-none border rounded-md py-3 px-3 flex items-center justify-center text-sm font-medium sm:flex-1',
                  ]"
                >
                  <RadioGroupLabel as="p">
                    {{ option.name }}
                  </RadioGroupLabel>
                </div>
              </RadioGroupOption>
            </div>
          </RadioGroup>
          <div
            class="mt-1"
            v-for="error of v$.gender.$errors"
            :key="error.$uid"
          >
            <div class="text-red-700">{{ error.$message }}</div>
          </div>
        </div>
        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Contact Information
          </h3>
        </div>

        <div class="sm:col-span-3">
          <label
            for="phoneNumber"
            class="block text-sm font-medium text-stone-400"
            >Phone Number*</label
          >
          <div class="mt-1">
            <input
              type="tel"
              @blur="v$.phoneNumber.$touch"
              v-model="formData.phoneNumber"
              name="phoneNumber"
              id="phoneNumber"
              autocomplete="off"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
              v-mask="'(###) ###-####'"
            />
            <div
              class=""
              v-for="error of v$.phoneNumber.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-3">
          <label
            for="email"
            class="block text-sm font-medium text-stone-400"
            >Email</label
          >
          <div class="mt-1">
            <input
              type="email"
              name="email"
              id="email"
              autocomplete="off"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              v-model="formData.email"
              placeholder=""
            />
            <div
              class=""
              v-for="error of v$.email.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Employer Information
          </h3>
          <p class="text-sm leading-5 text-gray-500">
            How do you prefer to add the employing company?
          </p>
        </div>
        <div class="sm:col-span-4">
          <fieldset class="mt-4">
            <legend class="sr-only">Company Capture Method</legend>
            <div class="space-y-4">
              <div class="flex items-center">
                <input
                  id="existing_company"
                  v-model="formData.companyCapture"
                  value="existing"
                  name="company_capture_method"
                  type="radio"
                  class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                />
                <div>
                  <label
                    class="flex ml-3 text-sm text-gray-700 itemsfont-medium"
                  >
                    <span>Use Existing Company</span>
                    <span class="ml-1">
                      <ArrowPathIcon
                        as="span"
                        class="w-4 h-4 text-stone-500"
                        v-if="!loading"
                        @click="refetch()"
                      />
                    </span>
                    <q-tooltip v-if="!loading" :offset="[0, 8]"
                      >Refresh Companies</q-tooltip
                    >

                    <q-spinner-ios
                      v-if="loading"
                      color="brown"
                      size="1.2em"
                    />
                    <q-tooltip v-if="loading" :offset="[0, 8]"
                      >Loading</q-tooltip
                    >
                  </label>
                </div>
              </div>
              <div
                v-if="formData.companyCapture == 'existing'"
                class="ml-7"
              >
                <div v-if="error">Error</div>

                <Combobox
                  as="div"
                  v-model="selectedCompany"
                  v-if="result && result.companies"
                >
                  <ComboboxLabel
                    class="block text-sm font-medium text-gray-700"
                    >Search</ComboboxLabel
                  >
                  <div class="relative mt-1">
                    <ComboboxInput
                      class="w-full py-2 pl-3 pr-10 bg-white border rounded-sm shadow-sm border-stone-300 focus:border-stone-300 focus:outline-none focus:ring-1 focus:ring-stone-500 sm:text-sm"
                      @change="query = $event.target.value"
                      :display-value="(company) => company.name"
                    />
                    <ComboboxButton
                      class="absolute inset-y-0 right-0 flex items-center px-2 rounded-r-md focus:outline-none"
                    >
                      <ChevronUpDownIcon
                        class="w-5 h-5 text-gray-400"
                        aria-hidden="true"
                      />
                    </ComboboxButton>

                    <ComboboxOptions
                      v-if="filteredCompanies.length > 0"
                      class="absolute z-10 w-full py-1 mt-1 overflow-auto text-base bg-white rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
                    >
                      <ComboboxOption
                        v-for="company in filteredCompanies"
                        :key="company.id"
                        :value="company"
                        as="template"
                        v-slot="{ active, selected }"
                      >
                        <li
                          :class="[
                            'relative cursor-default select-none py-2 pl-3 pr-9',
                            active
                              ? 'bg-stone-600 text-white'
                              : 'text-gray-900',
                          ]"
                        >
                          <span
                            :class="[
                              'block truncate',
                              selected && 'font-semibold',
                            ]"
                          >
                            {{ company.name }}
                          </span>

                          <span
                            v-if="selected"
                            :class="[
                              'absolute inset-y-0 right-0 flex items-center pr-4',
                              active
                                ? 'text-white'
                                : 'text-stone-600',
                            ]"
                          >
                            <CheckIcon
                              class="w-5 h-5"
                              aria-hidden="true"
                            />
                          </span>
                        </li>
                      </ComboboxOption>
                    </ComboboxOptions>
                  </div>
                </Combobox>
              </div>

              <div class="flex items-center">
                <input
                  id="add_company"
                  v-model="formData.companyCapture"
                  value="add_company"
                  name="company_capture_method"
                  type="radio"
                  class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                />
                <div>
                  <label
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Add Company
                  </label>
                </div>
              </div>
              <div
                v-if="formData.companyCapture == 'add_company'"
                class="ml-7"
              >
                <label
                  for="company_name"
                  class="block text-sm font-medium text-gray-700"
                  >Name</label
                >
                <div class="mt-1">
                  <input
                    type="text"
                    name="company_name"
                    id="company_name"
                    class="block w-full border-gray-300 rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm"
                    placeholder=""
                    v-model="formData.companyInput"
                  />
                </div>
              </div>

            </div>
          </fieldset>
        </div>

        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Employment Information
          </h3>
        </div>

        <div class="sm:col-span-4">
          <label
            for="employment_department"
            class="block text-sm font-medium text-stone-400"
            >Department*</label
          >
          <div class="mt-1">
            <input
              type="text"
              @blur="v$.employmentDepartment.$touch"
              v-model="formData.employmentDepartment"
              name="employment_epartment"
              id="employment_department"
              class="block w-full border-gray-300 rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm"
              placeholder=""
            />
            <div
              class=""
              v-for="error of v$.employmentDepartment.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>
        <div class="sm:col-span-4">
          <label
            for="employment_position"
            class="block text-sm font-medium text-stone-400"
            >Position*</label
          >
          <div class="mt-1">
            <input
              @blur="v$.employmentPosition.$touch"
              type="text"
              v-model="formData.employmentPosition"
              name="employment_position"
              id="employment_position"
              class="block w-full border-gray-300 rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm"
              placeholder=""
            />
            <div
              class=""
              v-for="error of v$.employmentPosition.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>
        <div class="sm:col-span-3">
          <v-date-picker
            color="teal"
            @blur="v$.employmentStartDate.$touch"
            v-model="formData.employmentStartDate"
            :masks="masks"
            class=""
          >
            <template v-slot="{ inputValue, inputEvents }">
              <label
                for="employment_start_date"
                class="block mb-1 text-sm font-medium text-stone-400"
                >Start of Employment*</label
              >
              <input
                class="w-full px-2 py-2 font-semibold border rounded-sm shadow-sm border-stone-300 focus:border-stone-300 focus:outline-none focus:ring-1 focus:ring-stone-500"
                :value="inputValue"
                v-on="inputEvents"
              />
            </template>
          </v-date-picker>
          <div
            class=""
            v-for="error of v$.employmentStartDate.$errors"
            :key="error.$uid"
          >
            <div class="text-red-700">{{ error.$message }}</div>
          </div>
        </div>
      </div>
    </div>
  </form>
</template>

<script setup>
import {
  ArrowSmallLeftIcon,
  CheckIcon,
  ChevronUpDownIcon,
  ArrowPathIcon,
} from "@heroicons/vue/24/outline";
import { reactive, ref, computed, watch } from "vue";
import useVuelidate from "@vuelidate/core";
import { useQuery, useMutation } from "@vue/apollo-composable";
import gql from "graphql-tag";
import { required, email, helpers } from "@vuelidate/validators";
import { useRouter } from "vue-router";
import { logErrorMessages } from "@vue/apollo-util";

const memoryOptions = [{ name: "Male" }, { name: "Female" }];
const masks = {
  input: "YYYY/MM/DD",
  mode: "date",
  data: ["L", "YYYY-MM-DD", "YYYY/MM/DD"],
};
const selectedCompany = ref({});
const query = ref("");
const COMPANIES = gql`
  query listCompanies($id: ID!) {
    companies(organisationId: $id) {
      id
      name
    }
  }
`;

watch(
  () => selectedCompany.value?.id,
  (id) => {
    formData.companySearch = id;
  },
);

const CAPTURE_PATIENT_FORM = gql`
  mutation createPatientCaptureForm(
    $organisationId: ID!
    $input: PatientCaptureFormInput!
  ) {
    createPatientCaptureForm(
      organisationId: $organisationId
      input: $input
    ) {
      success
      errors {
        path
        message
      }
      patient {
        lastName
        firstName

        employments {
          position
          department
        }

        employers {
          name
        }
      }
    }
  }
`;

const {
  mutate: sendForm,
  onDone,
  onError,
  loading: sendFormLoading,
} = useMutation(CAPTURE_PATIENT_FORM, () => ({
  variables: {
    organisationId: "1",
    input: {
      ...formData,
    },
  },
}));

const { result, loading, error, refetch } = useQuery(
  COMPANIES,
  { id: 1 },
  { fetchPolicy: "cache-and-network" },
);

const data = computed(() => result.value?.companies ?? []);

const filteredCompanies = computed(() =>
  query.value === ""
    ? data
    : data.value.filter((company) => {
        return company.name
          .toLowerCase()
          .includes(query.value.toLowerCase());
      }),
);

const rules = {
  identificationNumber: {
    required: helpers.withMessage(
      "Identification Number is required.",
      required,
    ),
  },
  firstName: {
    required: helpers.withMessage(
      "First Name is required.",
      required,
    ),
  },
  lastName: {
    required: helpers.withMessage("Last Name is required.", required),
  },
  dob: {
    required: helpers.withMessage(
      "Date of Birth is required.",
      required,
    ),
  },
  gender: {
    required: helpers.withMessage("Gender is required.", required),
  },
  phoneNumber: {
    required: helpers.withMessage("Mobile is required.", required),
  },
  email: { email: helpers.withMessage("Email is not valid.", email) },
  employmentDepartment: {
    required: helpers.withMessage(
      "Department is required.",
      required,
    ),
  },
  employmentPosition: {
    required: helpers.withMessage("Position is required.", required),
  },
  employmentStartDate: {
    required: helpers.withMessage(
      "Employment Start Date is required.",
      required,
    ),
  },
};

const formData = reactive({
  identificationNumber: "",
  firstName: "",
  lastName: "",
  dob: "",
  gender: "",
  email: "",
  phoneNumber: "",
  companyCapture: "existing",
  companySearch: "",
  companyInput: "",
  employmentDepartment: "",
  employmentPosition: "",
  employmentStartDate: "",
});

const v$ = useVuelidate(rules, formData);

const submitForm = async () => {
  const isFormCorrect = await v$.value.$validate();
  if (!isFormCorrect) return;
  sendForm();
};

const resetForm = () => {
  formData.identificationNumber = "";
  formData.firstName = "";
  formData.lastName = "";
  formData.dob = "";
  formData.gender = "";
  formData.email = "";
  formData.phoneNumber = "";
  formData.companyCapture = "existing";
  formData.companySearch = "";
  formData.companyInput = "";
  formData.employmentDepartment = "";
  formData.employmentPosition = "";
  formData.employmentStartDate = "";
};

const router = useRouter();

onDone((result) => {
  if (result.data.createPatientCaptureForm.success == true) {
    resetForm();
    router.push("/patients");
  } else {
    console.log(
      "From Error:",
      result.data?.createPatientCaptureForm.success,
    );
  }
});

onError((error) => {
  logErrorMessages(error);
});
</script>
