<template>
  <Transition
    name="cloud-fade"
    enter-active-class="transition-opacity duration-300 ease-in-out"
    leave-active-class="transition-opacity duration-300 ease-in-out"
    enter-from-class="opacity-0"
    enter-to-class="opacity-100"
    leave-from-class="opacity-100"
    leave-to-class="opacity-0"
  >
    <div
      v-if="isVisible"
      class="cloud-loading-indicator"
      :class="containerClasses"
      :title="tooltipText"
    >
      <CloudIcon
        :class="iconClasses"
        aria-hidden="true"
      />
    </div>
  </Transition>
</template>

<script setup>
import { computed } from 'vue'
import { CloudIcon } from '@heroicons/vue/24/outline'
import { useBackgroundLoading } from 'src/composables/use-background-loading'

const props = defineProps({
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  variant: {
    type: String,
    default: 'subtle',
    validator: (value) => ['subtle', 'prominent'].includes(value)
  },
  position: {
    type: String,
    default: 'relative',
    validator: (value) => ['relative', 'fixed', 'absolute'].includes(value)
  },
  showCount: {
    type: Boolean,
    default: false
  }
})

// Get background loading state
const { isBackgroundLoading, backgroundLoadingCount } = useBackgroundLoading()

// Computed properties
const isVisible = computed(() => isBackgroundLoading.value)

const tooltipText = computed(() => {
  if (!isBackgroundLoading.value) return ''
  
  const count = backgroundLoadingCount.value
  if (count === 1) {
    return 'Refreshing data in background...'
  }
  return `Refreshing ${count} queries in background...`
})

const containerClasses = computed(() => ({
  [`cloud-loading-indicator--${props.size}`]: true,
  [`cloud-loading-indicator--${props.variant}`]: true,
  [`cloud-loading-indicator--${props.position}`]: true,
  'cloud-loading-indicator--with-count': props.showCount && backgroundLoadingCount.value > 1
}))

const iconClasses = computed(() => ({
  'cloud-icon': true,
  [`cloud-icon--${props.size}`]: true,
  [`cloud-icon--${props.variant}`]: true,
  'cloud-icon--pulsing': isBackgroundLoading.value
}))
</script>

<style scoped>
.cloud-loading-indicator {
  @apply flex items-center justify-center;
}

.cloud-loading-indicator--relative {
  @apply relative;
}

.cloud-loading-indicator--fixed {
  @apply fixed;
}

.cloud-loading-indicator--absolute {
  @apply absolute;
}

/* Size variants */
.cloud-loading-indicator--small {
  @apply w-6 h-6;
}

.cloud-loading-indicator--medium {
  @apply w-8 h-8;
}

.cloud-loading-indicator--large {
  @apply w-10 h-10;
}

/* Cloud icon styles */
.cloud-icon {
  @apply transition-all duration-300 ease-in-out;
}

.cloud-icon--small {
  @apply w-4 h-4;
}

.cloud-icon--medium {
  @apply w-5 h-5;
}

.cloud-icon--large {
  @apply w-6 h-6;
}

/* Variant styles */
.cloud-icon--subtle {
  @apply text-gray-400;
}

.cloud-icon--prominent {
  @apply text-teal-500;
}

/* Pulsing animation */
.cloud-icon--pulsing {
  animation: cloud-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes cloud-pulse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

/* Hover effects */
.cloud-loading-indicator:hover .cloud-icon--subtle {
  @apply text-gray-600;
}

.cloud-loading-indicator:hover .cloud-icon--prominent {
  @apply text-teal-600;
}

/* Transition classes for enter/leave animations */
.cloud-fade-enter-active,
.cloud-fade-leave-active {
  transition: opacity 0.3s ease-in-out;
}

.cloud-fade-enter-from,
.cloud-fade-leave-to {
  opacity: 0;
}
</style>
