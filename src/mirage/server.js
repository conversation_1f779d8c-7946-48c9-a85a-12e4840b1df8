import { createServer, Factory } from "miragejs";
import { createGraphQLHandler } from "@miragejs/graphql";
import graphQLSchema from "../gql/schema.gql";
import { faker } from "@faker-js/faker";

export function makeServer({ environment = "development" } = {}) {
  let server = createServer({
    environment,

    factories: {
      patient: Factory.extend({
        first_name() {
          return faker.name.firstName();
        },
        last_name() {
          return faker.name.lastName();
        },
      }),
    },

    seeds(server) {
      server.createList("patient", 5);
    },

    routes() {
      this.urlPrefix = "http://localhost:3001";

      this.post(
        "/graphql",
        createGraphQLHandler(graphQLSchema, this.schema),
      );
    },
  });

  return server;
}
