import MainLayout from "../layouts/MainLayout.vue";
import IndexPage from "../pages/IndexPage.vue";
import MedicalIndexPage from "../pages/MedicalIndexPage.vue";
import OrganisationIndexPage from "../pages/OrganisationIndexPage.vue";
import MedicalNewPage from "../pages/MedicalNewPage.vue";
import UserMedicalNewPage from "../pages/UserMedicalNewPage.vue";
import PatientIndexPage from "../pages/PatientIndexPage.vue";
import PatientShowPage from "../pages/PatientShowPage.vue";
import CompanyIndexPage from "../pages/CompanyIndexPage.vue";
import CompanyShowPage from "../pages/CompanyShowPage.vue";
import CompanyNewPage from "../pages/CompanyNewPage.vue";
import CompanyEditPage from "../pages/CompanyEditPage.vue";
import SettingsPage from "../pages/SettingsPage.vue";
import PatientNewPage from "../pages/PatientNewPage.vue";
import PatientEditPage from "../pages/PatientEditPage.vue";
import DrugScreeningNewPage from "../pages/DrugScreeningNewPage.vue";
import AssessmentsIndexPage from "../pages/AssessmentsIndexPage.vue";
import EmploymentNewPage from "../pages/EmploymentNewPage.vue";
import EmploymentEditPage from "../pages/EmploymentEditPage.vue";
import LoginPage from "../pages/LoginPage.vue";
import MedicalShowPage from "../pages/MedicalShowPage.vue";
import MedicalEditPage from "../pages/MedicalEditPage.vue";
import ClinicNewPage from "../pages/ClinicNewPage.vue";
import ClinicEditPage from "../pages/ClinicEditPage.vue";
import PatientDocumentNewPage from "../pages/PatientDocumentNewPage.vue";
import PatientDocumentShowPage from "../pages/PatientDocumentShowPage.vue";
import PatientDocumentIndexPage from "../pages/PatientDocumentIndexPage.vue";
import AudioNewPage from "../pages/AudioNewPage.vue";
import AudioIndexPage from "../pages/AudioIndexPage.vue";
import SpiroNewPage from "../pages/SpiroNewPage.vue";
import SpiroIndexPage from "../pages/SpiroIndexPage.vue";
import VisionScreeningNewPage from "../pages/VisionScreeningNewPage.vue";
import PhysicalNewPage from "../pages/PhysicalAssessmentNewPage.vue";
import AnnexureThreeNewPage from "../pages/AnnexureThreeNewPage.vue";
import AudioShowPage from "../pages/AudioShowPage.vue";
import SpiroShowPage from "../pages/SpiroShowPage.vue";
import QuestionaireNewPageVue from "src/pages/QuestionaireNewPage.vue";
import PhysicalIndexPage from "src/pages/PhysicalIndexPage.vue";
import PhysicalShowPage from "src/pages/PhysicalShowPage.vue";
import VisionScreeningIndexPage from "src/pages/VisionScreeningIndexPage.vue";
import VisionScreeningShowPage from "src/pages/VisionScreeningShowPage.vue";
import VisionScreeningEditPage from "src/pages/VisionScreeningEditPage.vue";
import DrugScreeningIndexPage from "src/pages/DrugScreeningIndexPage.vue";
import DrugScreeningShowPage from "src/pages/DrugScreeningShowPage.vue";
import AudioEditPage from "src/pages/AudioEditPage.vue";
import SpiroEditPage from "src/pages/SpiroEditPage.vue";
import DrugScreeningEditPage from "src/pages/DrugScreeningShowPage.vue";
import DraftMedicalIndexPage from "src/pages/DraftMedicalIndexPage.vue";
import SignedOffMedicalIndexPage from "src/pages/SignedOffMedicalIndexPage.vue";

const routes = [
  {
    path: "/",
    component: MainLayout,
    children: [
      { path: "", component: IndexPage },
      {
        path: "spiros/:id/edit",
        name: "edit_spiro",
        component: SpiroEditPage,
      },
      {
        path: "employments/:id/edit",
        name: "edit_employment",
        component: EmploymentEditPage,
      },
      {
        path: "audios/:id/edit",
        name: "edit_audio",
        component: AudioEditPage,
      },
      {
        path: "patients/:patient_id/employments/new",
        name: "new_employment",
        component: EmploymentNewPage,
      },
      {
        path: "patients/:patient_id/spiros/new",
        name: "new_spiro",
        component: SpiroNewPage,
      },
      {
        path: "/spiro/:id",
        name: "show_spiro",
        component: SpiroShowPage,
      },
      {
        path: "/patients/:patient_id/questionaires/new",
        name: "new_questionaire",
        component: QuestionaireNewPageVue,
      },
      {
        path: "/draft_medicals",
        name: "draft_medicals",
        component: DraftMedicalIndexPage,
      },
      {
        path: "/signed_off_medicals",
        name: "signed_off_medicals",
        component: SignedOffMedicalIndexPage,
      },
      {
        path: "patients/:patient_id/spiros",
        name: "spiros",
        component: SpiroIndexPage,
      },
      {
        path: "patients/:patient_id/audios/new",
        name: "new_audio",
        component: AudioNewPage,
      },
      {
        path: "/audio/:id",
        name: "show_audio",
        component: AudioShowPage,
      },
      {
        path: "patients/:patient_id/audios",
        name: "audios",
        component: AudioIndexPage,
      },
      {
        path: "drug_screenings/:id",
        name: "show_drug_screening",
        component: DrugScreeningShowPage,
      },
      {
        path: "drug_screenings/:id/edit",
        name: "edit_drug_screening",
        component: DrugScreeningEditPage,
      },
      {
        path: "patients/:patient_id/drug_screenings",
        name: "drug_screenings",
        component: DrugScreeningIndexPage,
      },
      {
        path: "patients/:patient_id/drug_screenings/new",
        name: "new_drug_screening",
        component: DrugScreeningNewPage,
      },
      {
        path: "patients/:patient_id/annexure_three/new",
        name: "new_annexure_three",
        component: AnnexureThreeNewPage,
      },
      {
        path: "patients/:patient_id/physicals",
        name: "physicals",
        component: PhysicalIndexPage,
      },
      {
        path: "physical/:id/edit",
        name: "edit_physical",
        component: PhysicalNewPage,
      },
      {
        path: "patients/:patient_id/physical/new",
        name: "new_physical",
        component: PhysicalNewPage,
      },
      {
        path: "/physicals/:id",
        name: "show_physical",
        component: PhysicalShowPage,
      },
      {
        path: "patients/:patient_id/vision_screenings",
        name: "vision_screenings",
        component: VisionScreeningIndexPage,
      },
      {
        path: "vision_screenings/:id",
        name: "show_vision_screening",
        component: VisionScreeningShowPage,
      },
      {
        path: "vision_screenings/:id/edit",
        name: "edit_vision_screening",
        component: VisionScreeningEditPage,
      },

      {
        path: "patients/:patient_id/vision_screenings/new",
        name: "new_vision_screening",
        component: VisionScreeningNewPage,
      },
      {
        path: "patients/:patient_id/patient_documents/new",
        name: "new_patient_document",
        component: PatientDocumentNewPage,
      },
      {
        path: "/patient_documents/:id",
        name: "show_patient_document",
        component: PatientDocumentShowPage,
      },
      {
        path: "patients/:patient_id/patient_documents",
        name: "patient_documents",
        component: PatientDocumentIndexPage,
      },
      {
        path: "patients/:patient_id/assessments",
        name: "assessments",
        component: AssessmentsIndexPage,
      },
      {
        path: "organisations",
        component: OrganisationIndexPage,
      },
      {
        path: "medicals",
        name: "medicals",
        component: MedicalIndexPage,
      },
      {
        path: "medicals/:id",
        name: "show_medical",
        component: MedicalShowPage,
      },
      {
        path: "medicals/edit",
        name: "edit_medical",
        component: MedicalEditPage,
      },
      {
        path: "medicals/new",
        name: "new_medical",
        component: MedicalNewPage,
      },
      {
        path: "patients/:patient_id/medical/new",
        name: "new_user_medical",
        component: UserMedicalNewPage,
      },

      {
        path: "patients",
        name: "patients",
        component: PatientIndexPage,
      },
      {
        path: "patients/:id",
        name: "show_patient",
        component: PatientShowPage,
      },
      {
        path: "patients/new",
        component: PatientNewPage,
      },
      {
        path: "patients/:id/edit",
        name: "edit_patient",
        component: PatientEditPage,
      },
      {
        path: "companies",
        component: CompanyIndexPage,
      },
      {
        path: "companies/new",
        name: "new_company",
        component: CompanyNewPage,
      },
      {
        path: "companies/:id",
        name: "show_company",
        component: CompanyShowPage,
      },
      {
        path: "clinics/new",
        name: "new_clinic",
        component: ClinicNewPage,
      },
      {
        path: "clinic/:id/edit",
        name: "edit_clinic",
        component: ClinicEditPage,
      },
      {
        path: "companies/:id/edit",
        name: "edit_company",
        component: CompanyEditPage,
      },
      {
        path: "settings",
        component: SettingsPage,
      },
    ],
  },
  {
    path: "/login",
    name: "login",
    component: LoginPage,
  },

  // Always leave this as last one,
  // but you can also remove it
  {
    path: "/:catchAll(.*)*",
    component: () => import("pages/ErrorNotFound.vue"),
  },
];

export default routes;
