"""
An ISO 8601-encoded date
"""
scalar ISO8601Date

"""
An ISO 8601-encoded datetime
"""
scalar ISO8601DateTime

"""
Upload scalar for file uploads
"""
scalar Upload

# ============================================================================
# PATIENT TYPES
# ============================================================================

type Patient {
  id: ID!
  firstName: String
  lastName: String
  fullName: String
  email: String
  phoneNumber: String
  identificationNumber: String
  dob: ISO8601Date
  gender: String
  organisationId: Int!
  createdAt: ISO8601DateTime!
  updatedAt: ISO8601DateTime!

  # Relationships
  employments: [Employment!]!
  medicals: [Medical!]!
}

input PatientInput {
  firstName: String
  lastName: String
  email: String
  phoneNumber: String
  identificationNumber: String
  dob: ISO8601Date
  gender: String
  organisationId: Int
}

type PatientUpdatePayload {
  errors: [UserError!]!
  patient: Patient
}

type PatientCreatePayload {
  errors: [UserError!]!
  patient: Patient
}

# ============================================================================
# COMPANY TYPES
# ============================================================================

type Company {
  id: ID!
  name: String!
  address: String
  city: String
  province: String
  postalCode: String
  phoneNumber: String
  email: String
  contactPerson: String
  organisationId: Int!
  createdAt: ISO8601DateTime!
  updatedAt: ISO8601DateTime!

  # Relationships
  clinics: [Clinic!]!
  employments: [Employment!]!
}

type Clinic {
  id: ID!
  name: String!
  address: String
  city: String
  province: String
  postalCode: String
  phoneNumber: String
  email: String
  companyId: ID!
  organisationId: Int!
  createdAt: ISO8601DateTime!
  updatedAt: ISO8601DateTime!

  # Relationships
  company: Company!
}

input CompanyInput {
  name: String!
  address: String
  city: String
  province: String
  postalCode: String
  phoneNumber: String
  email: String
  contactPerson: String
  organisationId: Int
}

input ClinicInput {
  name: String!
  address: String
  city: String
  province: String
  postalCode: String
  phoneNumber: String
  email: String
  companyId: ID!
  organisationId: Int
}

type CompanyCreatePayload {
  errors: [UserError!]!
  company: Company
}

type CompanyUpdatePayload {
  errors: [UserError!]!
  company: Company
}

type ClinicCreatePayload {
  errors: [UserError!]!
  clinic: Clinic
}

type ClinicUpdatePayload {
  errors: [UserError!]!
  clinic: Clinic
}

# ============================================================================
# EMPLOYMENT TYPES
# ============================================================================

type Employment {
  id: ID!
  jobTitle: String
  department: String
  startDate: ISO8601Date
  endDate: ISO8601Date
  status: String
  patientId: ID!
  companyId: ID!
  organisationId: Int!
  createdAt: ISO8601DateTime!
  updatedAt: ISO8601DateTime!

  # Relationships
  patient: Patient!
  company: Company!
}

input EmploymentInput {
  jobTitle: String
  department: String
  startDate: ISO8601Date
  endDate: ISO8601Date
  status: String
  patientId: ID!
  companyId: ID!
  organisationId: Int
}

type EmploymentCreatePayload {
  errors: [UserError!]!
  employment: Employment
}

type EmploymentUpdatePayload {
  errors: [UserError!]!
  employment: Employment
}

# ============================================================================
# MEDICAL ASSESSMENT TYPES
# ============================================================================

type Medical {
  id: ID!
  name: String
  medicalType: String
  medicalExaminationDate: ISO8601Date
  medicalExpiryDate: ISO8601Date
  status: String
  outcome: String
  outcomeComment: String
  referralComment: String
  exclusionComment: String

  # Assessment flags
  audioPerformed: Boolean
  cannabisPerformed: Boolean
  ecgPerformed: Boolean
  heatPerformed: Boolean
  heightPerformed: Boolean
  physicalPerformed: Boolean
  spiroPerformed: Boolean
  visionPerformed: Boolean
  weightPerformed: Boolean

  # Assessment results
  audioResult: String
  cannabisResult: String
  ecgResult: String
  heatResult: String
  heightResult: String
  physicalResult: String
  spiroResult: String
  visionResult: String
  weightResult: String

  patientId: ID!
  clinicId: ID
  organisationId: Int!
  createdAt: ISO8601DateTime!
  updatedAt: ISO8601DateTime!

  # Relationships
  patient: Patient!
  clinic: Clinic
  assessments: [Assessment!]!
}

input MedicalInput {
  name: String
  medicalType: String
  medicalExaminationDate: ISO8601Date
  medicalExpiryDate: ISO8601Date
  status: String
  outcome: String
  outcomeComment: String
  referralComment: String
  exclusionComment: String

  # Assessment flags
  audioPerformed: Boolean
  cannabisPerformed: Boolean
  ecgPerformed: Boolean
  heatPerformed: Boolean
  heightPerformed: Boolean
  physicalPerformed: Boolean
  spiroPerformed: Boolean
  visionPerformed: Boolean
  weightPerformed: Boolean

  # Assessment results
  audioResult: String
  cannabisResult: String
  ecgResult: String
  heatResult: String
  heightResult: String
  physicalResult: String
  spiroResult: String
  visionResult: String
  weightResult: String

  patientId: ID!
  clinicId: ID
  organisationId: Int
}

type MedicalCreatePayload {
  errors: [UserError!]!
  medical: Medical
}

type MedicalUpdatePayload {
  errors: [UserError!]!
  medical: Medical
}

# ============================================================================
# ASSESSMENT TYPES
# ============================================================================

interface Assessment {
  id: ID!
  type: String!
  status: String
  result: String
  comments: String
  medicalId: ID!
  organisationId: Int!
  createdAt: ISO8601DateTime!
  updatedAt: ISO8601DateTime!
}

type AudioAssessment implements Assessment {
  id: ID!
  type: String!
  status: String
  result: String
  comments: String
  medicalId: ID!
  organisationId: Int!
  createdAt: ISO8601DateTime!
  updatedAt: ISO8601DateTime!

  # Audio-specific fields
  leftEar: String
  rightEar: String
  hearingAidUsed: Boolean
  testEnvironment: String
}

type SpiroAssessment implements Assessment {
  id: ID!
  type: String!
  status: String
  result: String
  comments: String
  medicalId: ID!
  organisationId: Int!
  createdAt: ISO8601DateTime!
  updatedAt: ISO8601DateTime!

  # Spiro-specific fields
  fvc: Float
  fev1: Float
  fev1FvcRatio: Float
  pef: Float
  interpretation: String
}

type VisionScreening implements Assessment {
  id: ID!
  type: String!
  status: String
  result: String
  comments: String
  medicalId: ID!
  organisationId: Int!
  createdAt: ISO8601DateTime!
  updatedAt: ISO8601DateTime!

  # Vision-specific fields
  leftEyeVision: String
  rightEyeVision: String
  colorVision: String
  glassesUsed: Boolean
  contactLensesUsed: Boolean
}

type DrugScreening implements Assessment {
  id: ID!
  type: String!
  status: String
  result: String
  comments: String
  medicalId: ID!
  organisationId: Int!
  createdAt: ISO8601DateTime!
  updatedAt: ISO8601DateTime!

  # Drug screening-specific fields
  sampleType: String
  testMethod: String
  substancesTested: [String!]!
  positiveResults: [String!]!
}

type PhysicalAssessment implements Assessment {
  id: ID!
  type: String!
  status: String
  result: String
  comments: String
  medicalId: ID!
  organisationId: Int!
  createdAt: ISO8601DateTime!
  updatedAt: ISO8601DateTime!

  # Physical-specific fields
  height: Float
  weight: Float
  bmi: Float
  bloodPressure: String
  heartRate: Int
  temperature: Float
}
