<template>
  <q-page padding>
    <PageHeading class="max-w-4xl">
      <template #navigation>
        <div class="flex items-center text-gray-500 text-uppercase">
          <ArrowSmallLeftIcon class="w-5 h-5" />
          <router-link :to="backRoute">Back</router-link>
        </div>
      </template>
      <template #heading>
        <div class="capitalize">{{ audio.name }}</div>
      </template>
    </PageHeading>

    <div class="max-w-4xl">
      <div
        class="flex flex-col p-4 rounded-sm shadow md:mt-8 bg-stone-50"
      >
        <div class="flex justify-between">
          <div class="mb-6 font-bold text-stone-700">
            Audiometry Details
          </div>

          <span class="justify-end rounded-md">
            <router-link
              class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs leading-4 font-medium rounded text-stone-100 bg-stone-800 hover:text-stone-100 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:text-gray-800 active:bg-gray-50 transition ease-in-out duration-150"
              :to="{ name: 'edit_audio', id: route.params.id }"
            >
              Edit Audio
            </router-link>
          </span>
        </div>

        <div class="max-w-4xl">
          <div class="grid grid-cols-2 gap-4">
            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-400"
              >
                Patient
              </div>
              <div class="font-bold">
                {{ audio.patient?.fullName }}
              </div>
            </div>
            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-400"
              >
                System Used
              </div>
              <div class="font-bold">
                {{
                  audio.systemUsed
                    ? audio.systemUsed
                    : "-- not specified --"
                }}
              </div>
            </div>
            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-400"
              >
                Date Performed
              </div>
              <div class="font-bold">
                {{
                  audio.datePerformed
                    ? audio.datePerformed
                    : "-- not specified --"
                }}
              </div>
            </div>

            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-400"
              >
                Clinic Used
              </div>
              <div class="font-bold">
                {{
                  audio.clinic?.clinicName
                    ? audio.clinic?.clinicName
                    : "-- not specified --"
                }}
              </div>
            </div>

            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-400"
              >
                Performed by
              </div>
              <div class="font-bold">
                {{
                  audio.performedBy
                    ? audio.performedBy
                    : "-- not specified --"
                }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="max-w-4xl">
        <div
          class="flex flex-col p-4 mt-8 rounded-sm shadow bg-stone-50"
        >
          <div class="sm:col-span-4">
            <h3 class="text-lg font-semibold leading-6 text-gray-900">
              Notes
            </h3>
            <p class="text-sm leading-5 text-gray-500">
              {{ audio.note ? audio.note : "No notable comments" }}
            </p>
          </div>
        </div>
      </div>

      <div class="max-w-4xl">
        <div
          class="flex flex-col p-4 mt-8 rounded-sm shadow bg-stone-50"
        >
          <div class="sm:col-span-4">
            <h3 class="text-lg font-semibold leading-6 text-gray-900">
              Result
            </h3>
            <p class="text-sm leading-5 text-gray-500">
              {{
                audio.result ? audio.result : "No notable comments"
              }}
            </p>
          </div>
        </div>
      </div>

      <div class="max-w-md">
        <div class="mt-10">
          <div class="" v-if="reportCount > 0">
            <div class="sm:flex sm:items-center">
              <div class="sm:flex-auto">
                <h1
                  class="flex items-center text-xl font-semibold text-gray-900"
                >
                  Reports ({{ reportCount }})
                  <label
                    class="flex ml-3 text-sm font-medium text-gray-700"
                  >
                    <span class="ml-1">
                      <ArrowPathIcon
                        as="span"
                        class="w-4 h-4 text-stone-500"
                        v-if="!audioLoading"
                        @click="audioRefetch()"
                      />
                    </span>
                    <q-tooltip v-if="!audioLoading" :offset="[0, 8]"
                      >Refetch Reports</q-tooltip
                    >

                    <q-spinner-ios
                      v-if="audioLoading"
                      color="brown"
                      size="1.2em"
                    />
                    <q-tooltip v-if="audioLoading" :offset="[0, 8]"
                      >Loading</q-tooltip
                    >
                  </label>
                </h1>
                <p class="mt-2 text-sm text-gray-700">
                  A list of attachments for this audio
                </p>
              </div>
            </div>
            <div class="flex flex-col mt-8">
              <div
                class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"
              >
                <div
                  class="inline-block min-w-full px-6 py-2 align-middle lg:px-8"
                >
                  <div
                    class="overflow-hidden rounded-sm shadow ring-1 ring-black ring-opacity-5"
                  >
                    <table
                      class="min-w-full divide-y divide-gray-300"
                    >
                      <thead class="bg-stone-50">
                        <tr>
                          <th
                            scope="col"
                            class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-4"
                          >
                            File Attachment
                          </th>
                        </tr>
                      </thead>
                      <tbody
                        class="bg-white divide-y divide-gray-200"
                      >
                        <tr
                          v-for="report in reports"
                          :key="report.id"
                        >
                          <td
                            class="flex justify-between py-2 pl-6 text-sm text-gray-500 whitespace-nowrap"
                          >
                            <span>
                              {{ report.fileName }}
                            </span>
                            <span class="mr-4">
                              <a :href="report.url" target="_blank">
                                <ArrowDownTrayIcon
                                  class="w-5 h-5 text-teal-900 hover:text-teal-700"
                                />
                              </a>
                            </span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="" v-else>
            <div class="sm:flex sm:items-center">
              <div class="sm:flex-auto">
                <h1
                  class="flex items-center text-xl font-semibold text-gray-900"
                >
                  Reports
                  <label
                    class="flex ml-3 text-sm font-medium text-gray-700"
                  >
                    <span class="ml-1">
                      <ArrowPathIcon
                        as="span"
                        class="w-4 h-4 text-stone-500"
                        v-if="!audioLoading"
                        @click="audioRefetch()"
                      />
                    </span>
                    <q-tooltip v-if="!audioLoading" :offset="[0, 8]"
                      >Refetch Reports</q-tooltip
                    >

                    <q-spinner-ios
                      v-if="audioLoading"
                      color="brown"
                      size="1.2em"
                    />
                    <q-tooltip v-if="audioLoading" :offset="[0, 8]"
                      >Loading</q-tooltip
                    >
                  </label>
                </h1>
                <p class="mt-2 text-sm text-gray-700">
                  A list of generated reports for this medical
                </p>
              </div>
            </div>
            <div class="flex flex-col mt-8">
              <div
                class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"
              >
                <div
                  class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8"
                >
                  <div
                    class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg"
                  >
                    <table
                      class="min-w-full divide-y divide-gray-300"
                    >
                      <thead class="bg-stone-50">
                        <tr>
                          <th
                            scope="col"
                            class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"
                          ></th>
                        </tr>
                      </thead>
                      <tbody
                        class="flex items-center justify-center p-4 bg-white divide-y divide-gray-200"
                      >
                        <tr class="">
                          <EmptyState
                            class="flex justify-center p-4"
                          />
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { useRoute, useRouter } from "vue-router";
import { useQuery, useMutation } from "@vue/apollo-composable";
import gql from "graphql-tag";
import { computed, ref, reactive } from "vue";
import {
  ArrowSmallLeftIcon,
  ArrowDownTrayIcon,
  ArrowPathIcon,
} from "@heroicons/vue/24/outline";
import { Dialog, DialogTitle } from "@headlessui/vue";

const route = useRoute();

const GET_AUDIO = gql`
  query getAudio($id: ID!) {
    audio(id: $id) {
      attachments {
        id
        url
        fileName
      }
      name
      note
      datePerformed
      performedBy
      result
      systemUsed
      patient {
        fullName
        id
      }
      clinic {
        id
        clinicName
      }
    }
  }
`;

const {
  result: audioResult,
  refetch: audioRefetch,
  loading: audioLoading,
} = useQuery(
  GET_AUDIO,
  { id: route.params.id },
  { fetchPolicy: "cache-and-network" },
);

const audio = computed(() => {
  return audioResult.value?.audio ?? "No Audio";
});

const reports = computed(() => {
  return audio.value?.attachments;
});

const patient = computed(() => {
  return audio.value?.patient;
});

const backRoute = computed(() => {
  return {
    name: "audios",
    params: { patient_id: patient.value?.id ?? "Broken" },
  };
});
const reportCount = computed(() => {
  return audio.value?.attachments?.length ?? 0;
});
</script>
