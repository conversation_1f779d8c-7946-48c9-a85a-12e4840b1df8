<template>
  <q-page padding>
    <PageHeading>
      <template #navigation>
        <div class="flex items-center text-gray-500 text-uppercase">
          <ArrowSmallLeftIcon class="w-5 h-5" />
          <router-link to="/patients">Back</router-link>
        </div>
      </template>
      <template #heading>
        <div class="flex items-center">
          <div class="capitalize">
            {{ patient.fullName }}
          </div>
        </div>
      </template>
    </PageHeading>
    <div class="max-w-4xl">
      <div
        class="flex flex-col p-4 rounded-sm shadow md:mt-8 bg-stone-50"
      >
        <div class="flex justify-between">
          <div class="mb-6 font-bold text-stone-700">Details</div>

          <span class="justify-end rounded-md">
            <router-link
              class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs leading-4 font-medium rounded text-stone-100 bg-stone-800 hover:text-stone-100 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:text-gray-800 active:bg-gray-50 transition ease-in-out duration-150"
              :to="{ name: 'edit_patient', id: route.params.id }"
            >
              Edit Patient
            </router-link>
          </span>
        </div>
        <div class="max-w-4xl">
          <div class="grid grid-cols-2 gap-4">
            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-500"
              >
                First Name
              </div>
              <div class="font-bold capitalize">
                {{ patient.firstName }}
              </div>
            </div>
            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-500"
              >
                Last Name
              </div>
              <div class="font-bold capitalize">
                {{ patient.lastName }}
              </div>
            </div>

            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-500"
              >
                Date of Birth
              </div>
              <div class="font-bold capitalize">
                {{ patient.dob }}
              </div>
            </div>

            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-500"
              >
                Gender
              </div>
              <div class="font-bold capitalize">
                {{ patient.gender }}
              </div>
            </div>

            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-500"
              >
                Identification Number
              </div>
              <div class="font-bold capitalize">
                {{ patient.identificationNumber }}
              </div>
            </div>

            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-500"
              >
                Mobile
              </div>
              <div class="font-bold">
                {{
                  patient.phoneNumber
                    ? patient.phoneNumber
                    : "-- no data --"
                }}
              </div>
            </div>

            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-500"
              >
                Email
              </div>
              <div class="font-bold">
                {{ patient.email ? patient.email : "-- no data --" }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="max-w-4xl">
        <div class="mt-10">
          <div
            class="grid grid-cols-1 row-gap-6 col-gap-2 mt-4 sm:grid-cols-2"
          >
            <dt class="text-sm font-medium leading-5 text-stone-500">
              Recent Medical Certificates
            </dt>
            <span class="inline-flex justify-end rounded-md">
              <router-link
                class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs leading-4 font-medium rounded text-stone-100 bg-stone-800 hover:text-stone-100 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:text-gray-800 active:bg-gray-50 transition ease-in-out duration-150"
                :to="{
                  name: 'new_user_medical',
                  params: { patient_id: route.params.id },
                }"
              >
                Add Medical Certificate
              </router-link>
            </span>

            <dd
              class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2"
            >
              <div
                class="mt-4 overflow-hidden shadow bg-stone-50 sm:rounded-sm"
              >
                <ul>
                  <li
                    class="border-t border-gray-200 first:border-gray-100"
                    v-for="medical in patient.medicals"
                    :key="medical.id"
                  >
                    <router-link
                      :to="{
                        name: 'show_medical',
                        params: { id: medical.id },
                      }"
                      class="block transition duration-150 ease-in-out hover:bg-gray-50 focus:outline-none focus:bg-gray-50"
                    >
                      <div
                        class="flex items-center px-4 py-4 sm:px-6"
                      >
                        <div class="flex items-center flex-1 min-w-0">
                          <div
                            class="flex-1 min-w-0 px-4 md:grid md:grid-cols-2 md:gap-4"
                          >
                            <div>
                              <div
                                class="text-sm font-medium leading-5 truncate text-stone-600"
                              >
                                {{ medical.name }}
                              </div>
                              <div
                                class="flex items-center mt-2 text-sm leading-5 text-gray-500"
                              >
                                <svg
                                  class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"
                                  fill="currentColor"
                                  viewBox="0 0 20 20"
                                >
                                  <path
                                    fill-rule="evenodd"
                                    d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884zM18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"
                                    clip-rule="evenodd"
                                  />
                                </svg>
                                <span class="truncate"
                                  >Valid until
                                  {{
                                    medical.medicalExpiryDate
                                  }}</span
                                >
                              </div>
                            </div>
                            <div class="hidden md:block">
                              <div>
                                <div
                                  class="text-sm leading-5 text-gray-900"
                                >
                                  Created on
                                  <time datetime="2020-01-07"
                                    >{{ medical.created_at }}
                                  </time>
                                </div>

                                <div
                                  class="flex items-center mt-2 text-sm leading-5 text-gray-500"
                                >
                                  <!-- <% if medical.evaluation_signoff %>
                              <svg
                                class="flex-shrink-0 mr-1.5 h-5 w-5 text-green-400"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path
                                  fill-rule="evenodd"
                                  d="M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                  clip-rule="evenodd"
                                />
                              </svg>
                              Signed on <%=
                              medical.evaluation_signoff.created_at.strftime("%d
                              %B %Y") %> <% else %> --- Not yet signed
                              off --- <% end %> -->
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                        <div>
                          <svg
                            clas="h-5 w-5 text-gray-400"
                            fill="currentColor"
                            viewBox="0 0 20 20"
                          >
                            <path
                              fil-rule="evenodd"
                              d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                              clip-rule="evenodd"
                            />
                          </svg>
                        </div>
                      </div>
                    </router-link>
                  </li>
                </ul>
              </div>
            </dd>
          </div>
        </div>

        <div class="mt-10">
          <div
            class="grid grid-cols-1 row-gap-6 col-gap-2 mt-4 sm:grid-cols-2"
          >
            <dt class="text-sm font-medium leading-5 text-gray-500">
              Employment History
            </dt>
            <span class="inline-flex justify-end rounded-md">
              <router-link
                :to="{
                  name: 'new_employment',
                  params: { patient_id: route.params.id },
                }"
                class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs leading-4 font-medium rounded text-stone-100 bg-stone-800 hover:text-gray-100 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:text-gray-800 active:bg-gray-50 transition ease-in-out duration-150"
              >
                Add Employment
              </router-link>
            </span>

            <dd
              class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2"
            >
              <div
                class="mt-4 overflow-hidden shadow bg-stone-50 sm:rounded-sm"
              >
                <ul>
                  <li
                    v-for="employment in patient.employments"
                    :key="employment.id"
                    class="border-t border-gray-200 first:border-gray-100"
                  >
                    <router-link
                      :to="{ path: 'patients' }"
                      class="block transition duration-150 ease-in-out hover:bg-gray-50 focus:outline-none focus:bg-gray-50"
                    >
                      <div
                        class="flex items-center px-4 py-4 sm:px-6"
                      >
                        <div class="flex items-center min-w-0 flex-">
                          <div
                            class="min-w-0 px-4 flex1 md:grid md:grid-cols-2 md:gap-4"
                          >
                            <div>
                              <div
                                class="text-sm font-medium truncate text-stone-600 leding-5"
                              >
                                {{ employment.company.name }} &middot;
                                {{ employment.position }}
                              </div>
                              <div
                                class="flex mt-2 text-sm leading-5 text-gray-500 iems-center"
                              >
                                <svg
                                  class="flex-shrink-0 mr-1.5 h-5 w-5 text-gray-400"
                                  fill="currentColor"
                                  viewBox="0 0 20 20"
                                >
                                  <path
                                    fill-rule="evendd"
                                    d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884zM18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"
                                    clip-rule="evenodd"
                                  />
                                </svg>
                                <span class="truncate"
                                  >Inducted on
                                  {{ employment.inductionDate }}</span
                                >
                              </div>
                            </div>
                            <div class="hidden md:block">
                              <div>
                                <div
                                  class="text-sm leading-5 ext-gray-900"
                                >
                                  <!-- <% if
                              employment.termination_date.present? %>
                              Terminated on
                              <time
                                datetime="<%"
                                ="#{ employment.termination_date }"
                                %
                                >><%#
                                employment.termination_date.strftime("%d
                                %B %Y") %></time
                              >
                              <% end %> -->
                                </div>
                                <!-- <div
                              class="flex mt-2 text-sm leading-5 text-gray-500 items-cente"
                            >
                              <% if employment.active? %>
                              <svg
                                class="flex-shrink-0 mr-1.5 h-5 w-5 text-green-400"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                              >
                                <path
                                  fill-rule="evenodd"
                                  d="M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                                  clip-rule="evenodd"
                                />
                              </svg>
                              Employment active <% else %>
                              <svg
                                class="flex-shrink-0 mr-1.5 h-5 w-5 text-red-600"
                                fill="currentColor"
                                viewBox="0 0 20 20"
                                xmlns="http://www.w3.org/2000/svg"
                              >
                                <path
                                  fill-rule="evenodd"
                                  d="M10 18a8 8 0 1 0 0-16 8 8 0 0 0 0 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 1 0 1.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                  clip-rule="evenodd"
                                ></path>
                              </svg>
                              <span class="truncate">
                                Terminated due to: <%=
                                employment.termination_reason %>
                              </span>

                              <% end %> -->
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <div>
                        <svg
                          class="w-5 h-5 ext-gray-400"
                          fill="currentClor"
                          viewBox="0 0 20 20"
                        >
                          <path
                            fill-rule="evenodd"
                            d="M7.293 14.707a1 1 0 0 1 0-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z"
                            clip-rule="evenodd"
                          />
                        </svg>
                      </div>
                    </router-link>
                  </li>
                </ul>
              </div>
            </dd>
          </div>
        </div>

        <div
          class="grid row-gap-6 col-gap-2 mt-8 grd-cols-1 sm:grid-cols-2"
        >
          <dt
            class="flex justify-between text-sm font-medium leading-5 text-stone-600"
          >
            <!-- Medical History (wip) -->
          </dt>
          <span class="inline-flex justify-end rounded-md">
            <router-link
              class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs leading-4 font-medium rounded text-stone-200 bg-stone-800 hover:text-gray-100 focus:outline-none focus:border-stone-300 focus:shadow-outline-blue active:text-stone-800 active:bg-stone-50 transition ease-in-out duration-150"
              :to="{
                name: 'assessments',
                params: { patient_id: route.params.id },
              }"
            >
              Add Medical Information
            </router-link>
          </span>
          <!-- <dd
            class="mt-1 text-sm leading-5 text-gray-900 sm:mt-0 sm:col-span-2"
          >
            <ul>
              <div class="relative">
                <div
                  class="absolute top-0 z-0 h-full border-r-2 border-gray-200 border-solid"
                  style="left: 19px"
                ></div>
                <ul class="p-0 m-0 list-none">
                  <li class="pb-6">
                    <div class="flex items-center mb-1">
                      <div
                        class="z-10 flex items-center justify-center w-10 h-10 bg-white border-2 border-gray-200 rounded-full"
                      >
                        <svg
                          class="w-6 text-stone-600"
                          fill="none"
                          stroke="currentColor" viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M20.618 5.984A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016zM12 9v2m0 4h.01"
                          ></path>
                        </svg>
                      </div>
                      <div class="flex-1 ml-4 text-xs text-gray-500">
                        at 07 September 2022 - 13:46 ·
                        <span class="text-stone-600"
                          ><a
                            data-turbolinks="false"
                            href="/exclusions/1/edit"
                            >edit</a
                          ></span
                        >
                      </div>
                    </div>
                    <div
                      class="inline-block p-2 ml-12 text-gray-800 rounded-md bg-stone-100"
                    >
                      <span class="mb-2 text-stone-600">
                        <a href="/exclusions/1/edit">exclusion</a>
                      </span>
                      - toes
                    </div>
                  </li>

                  <li class="pb-6">
                    <div class="flex items-center mb-1">
                      <div
                        class="z-10 flex items-center justify-center w-10 h-10 bg-white border-2 border-gray-200 rounded-full"
                      >
                        <svg
                          class="w-6 text-stone-600"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
                          ></path>
                        </svg>
                      </div>
                      <div class="flex-1 ml-4 text-xs text-gray-500">
                        at 06 September 2022 - 10:50 ·
                        <span class="text-stone-600"
                          ><a
                            data-turbolinks="false"
                            href="/referrals/1/edit"
                            >edit</a
                          ></span
                        >
                      </div>
                    </div>
                    <div
                      class="inline-block p-2 ml-12 text-gray-800 rounded-md bg-stone-100"
                    >
                      <span class="mb-2 text-stone-600">
                        <a href="/referrals/1/edit">Referral</a>
                      </span>
                      - asdds
                    </div>
                  </li>
                </ul>
              </div>
            </ul>
          </dd> -->
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { useRoute } from "vue-router";
import { useQuery } from "@vue/apollo-composable";
import gql from "graphql-tag";
import { computed } from "vue";
import { ArrowSmallLeftIcon } from "@heroicons/vue/24/outline";
const route = useRoute();

const PATIENT = gql`
  query getPatient($id: ID!) {
    patient(id: $id) {
      id
      firstName
      lastName
      fullName
      dob
      gender
      identificationNumber
      phoneNumber
      email
      medicals {
        id
        name
        medicalExpiryDate
      }
      employments {
        id
        position
        inductionDate
        company {
          name
          id
        }
      }
    }
  }
`;
const { result } = useQuery(
  PATIENT,
  { id: route.params.id },
  { fetchPolicy: "cache-and-network" },
);

const patient = computed(() => {
  return result.value?.patient ?? "Patient";
});
</script>
