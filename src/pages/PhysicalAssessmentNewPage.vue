<template>
  <q-page padding>
    <form @submit.prevent="submitForm" class="max-w-3xl">
      <PageHeading>
        <template #navigation>
          <div class="flex items-center text-gray-500 text-uppercase">
            <router-link
              class="flex"
              :to="{
                name: 'physicals',
                params: { patient_id: route.params.patient_id },
              }"
            >
              <ArrowSmallLeftIcon class="w-5 h-5" />
              Back
            </router-link>
          </div>
        </template>
        <template #heading>
          <div class="capitalize">New Physical assessment</div>
        </template>

        <template #buttons>
          <q-btn
            flat
            class="text-white bg-teal-700 text-bold"
            padding="md"
            no-caps
            :loading="sendFormLoading"
            type="submit"
          >
            <template v-slot:loading>
              <q-spinner-ios />
            </template>
            <CheckIcon class="w-5 h-5 mr-1 font-bold" />
            <div class="text-sm">Save</div>
          </q-btn>
        </template>
      </PageHeading>
    </form>

    <div
      class="flex flex-col max-w-3xl p-4 rounded-sm shadow md:mt-8 bg-stone-50"
    >
      <div class="max-w-4xl">
        <div class="grid grid-cols-2 gap-4">
          <div class="flex flex-col">
            <div
              class="mb-1 text-xs font-bold uppercase text-stone-400"
            >
              Patient fullname
            </div>
            <div class="font-bold">
              {{ patient.fullName }}
            </div>
          </div>
          <div class="flex flex-col">
            <div
              class="mb-1 text-xs font-bold uppercase text-stone-400"
            >
              ID Number
            </div>
            <div class="font-bold">
              {{ patient.identificationNumber }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-2xl">
      <div
        class="grid grid-cols-1 mt-6 gap-x-4 gap-y-6 sm:grid-cols-6"
      >
        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Vitals
          </h3>
        </div>

        <div class="sm:col-span-3">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Height (cm) *</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              v-model="formData.height"
              id="name"
              @blur="v$.height.$touch"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />

            <div
              class=""
              v-for="error of v$.height.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-3">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Weight (kg) *</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              v-model="formData.weight"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              @blur="v$.weight.$touch"
              placeholder=""
            />

            <div
              class=""
              v-for="error of v$.weight.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="result"
            class="block text-sm font-medium text-stone-400"
          >
            Blood Pressure*</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              v-model="formData.bloodPressure"
              @blur="v$.bloodPressure.$touch"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <div
              class=""
              v-for="error of v$.bloodPressure.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-3">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Pulse*</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              v-model="formData.pulse"
              @blur="v$.pulse.$touch"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />

            <div
              class=""
              v-for="error of v$.pulse.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-3">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Blood Sugar*</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              v-model="formData.bloodSugar"
              @blur="v$.bloodSugar.$touch"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />

            <div
              class=""
              v-for="error of v$.bloodSugar.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Tests
          </h3>
        </div>

        <div class="sm:col-span-4">
          <fieldset class="space-y-4">
            <legend class="sr-only">Medical Investigations</legend>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="nad"
                  aria-describedby="nad"
                  name="nad"
                  type="checkbox"
                  v-model="formData.nadTest"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label for="nad" class="font-medium text-gray-700">
                  NAD</label
                >
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="leucocytes"
                  v-model="formData.leucocytesTest"
                  aria-describedby="leucocytes"
                  name="leucocytes"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="leucocytes"
                  class="font-medium text-gray-700"
                  >Leucocytes</label
                >
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="nitrite"
                  aria-describedby="nitrite"
                  name="nitrite"
                  v-model="formData.nitriteTest"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label for="nitrite" class="font-medium text-gray-700"
                  >Nitrite</label
                >
              </div>
            </div>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="bloodTest"
                  aria-describedby="bloodTest"
                  name="bloodTest"
                  v-model="formData.bloodTest"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="bloodTest"
                  class="font-medium text-gray-700"
                  >Blood</label
                >
              </div>
            </div>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="proteinTest"
                  aria-describedby="proteinTest"
                  name="proteinTest"
                  v-model="formData.proteinTest"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="proteinTest"
                  class="font-medium text-gray-700"
                  >Protein</label
                >
              </div>
            </div>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="glucoseTest"
                  aria-describedby="glucoseTest"
                  name="glucoseTest"
                  type="checkbox"
                  v-model="formData.glucoseTest"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="glucoseTest"
                  class="font-medium text-gray-700"
                  >Glucose</label
                >
              </div>
            </div>
          </fieldset>
        </div>

        <div class="sm:col-span-5">
          <label
            for="description"
            class="block text-sm font-medium text-stone-400"
            >General Appearance</label
          >
          <div class="mt-1">
            <textarea
              autocomplete="off"
              row="4"
              type="text"
              name="about"
              id="about"
              @blur="v$.generalAppearance.$touch"
              v-model="formData.generalAppearance"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800 placeholder:text-slate-300 placeholder:font-normal"
              placeholder=""
            ></textarea>
            <div
              class=""
              v-for="error of v$.generalAppearance.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Systemic Examinations
          </h3>
        </div>

        <div class="sm:col-span-4">
          <fieldset class="space-y-4">
            <legend class="sr-only">Systemic Examinations</legend>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="peripheralExam"
                  aria-describedby="peripheralExam"
                  name="peripheralExam"
                  v-model="formData.peripheralExam"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="peripheralExam"
                  class="font-medium text-gray-700"
                  >Peripheral Signs</label
                >
              </div>
            </div>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="skinExam"
                  aria-describedby="skinExam"
                  name="skinExam"
                  v-model="formData.skinExam"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="skinExam"
                  class="font-medium text-gray-700"
                >
                  Skin or Appendages</label
                >
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="entExam"
                  aria-describedby="entExam"
                  name="entExam"
                  v-model="formData.entExam"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label for="entExam" class="font-medium text-gray-700"
                  >ENT</label
                >
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="cvsExam"
                  aria-describedby="cvsExam"
                  name="cvsExam"
                  type="checkbox"
                  v-model="formData.cvsExam"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label for="cvsExam" class="font-medium text-gray-700"
                  >CVS</label
                >
              </div>
            </div>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="chestExam"
                  aria-describedby="chestExam"
                  v-model="formData.chestExam"
                  name="chestExam"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="chestExam"
                  class="font-medium text-gray-700"
                  >Chest</label
                >
              </div>
            </div>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="gastroExam"
                  aria-describedby="gastroExam"
                  name="gastroExam"
                  v-model="formData.gastroExam"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="gastroExam"
                  class="font-medium text-gray-700"
                  >Gastro Intestinal System</label
                >
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="urinaryExam"
                  aria-describedby="urinaryExam"
                  name="urinaryExam"
                  v-model="formData.urinaryExam"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="urinaryExam"
                  class="font-medium text-gray-700"
                  >Urinary System</label
                >
              </div>
            </div>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="musculoExam"
                  aria-describedby="musculoExam"
                  name="musculoExam"
                  v-model="formData.musculoExam"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="musculoExam"
                  class="font-medium text-gray-700"
                  >Musculo Skeletal System</label
                >
              </div>
            </div>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="cnsExam"
                  aria-describedby="cnsExam"
                  name="cnsExam"
                  v-model="formData.cnsExam"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label for="cnsExam" class="font-medium text-gray-700"
                  >CNS</label
                >
              </div>
            </div>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="endocrineExam"
                  aria-describedby="endocrineExam"
                  name="endocrineExam"
                  v-model="formData.endocrineExam"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="endocrineExam"
                  class="font-medium text-gray-700"
                  >Endocrine System</label
                >
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="glucoseExam"
                  aria-describedby="glucoseExam"
                  name="glucoseExam"
                  v-model="formData.glucoseExam"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="glucoseExam"
                  class="font-medium text-gray-700"
                  >Glucose</label
                >
              </div>
            </div>
          </fieldset>
        </div>

        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Chronic Diseases
          </h3>
        </div>

        <div class="sm:col-span-4">
          <fieldset class="space-y-4">
            <legend class="sr-only">Chronic Diseases</legend>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="hypertensionChronic"
                  aria-describedby="hypertensionChronic"
                  name="hypertensionChronic"
                  v-model="formData.hypertensionChronic"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="hypertensionChronic"
                  class="font-medium text-gray-700"
                  >Hypertension</label
                >
              </div>
            </div>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="asthmaChronic"
                  aria-describedby="asthmaChronic"
                  name="asthmaChronic"
                  v-model="formData.asthmaChronic"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="asthmaChronic"
                  class="font-medium text-gray-700"
                >
                  Asthma</label
                >
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="epilepsyChronic"
                  aria-describedby="epilepsyChronic"
                  name="epilepsyChronic"
                  v-model="formData.epilepsyChronic"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="epilepsyChronic"
                  class="font-medium text-gray-700"
                  >Epilepsy</label
                >
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="mentalChronic"
                  aria-describedby="mentalChronic"
                  name="mentalChronic"
                  v-model="formData.mentalChronic"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="mentalChronic"
                  class="font-medium text-gray-700"
                  >Mental</label
                >
              </div>
            </div>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="obesityChronic"
                  aria-describedby="obesityChronic"
                  name="obesityChronic"
                  v-model="formData.obesityChronic"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="obesityChronic"
                  class="font-medium text-gray-700"
                  >Obesity</label
                >
              </div>
            </div>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="diabetesChronic"
                  aria-describedby="diabetesChronic"
                  name="diabetesChronic"
                  v-model="formData.diabetesChronic"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="diabetesChronic"
                  class="font-medium text-gray-700"
                  >Diabetes</label
                >
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="drugChronic"
                  aria-describedby="drugChronic"
                  name="drugChronic"
                  v-model="formData.drugChronic"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="drugChronic"
                  class="font-medium text-gray-700"
                  >Drug/Alcohol</label
                >
              </div>
            </div>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="thyroidChronic"
                  aria-describedby="thyroidChronic"
                  name="thyroidChronic"
                  v-model="formData.thyroidChronic"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="thyroidChronic"
                  class="font-medium text-gray-700"
                  >Thyroid</label
                >
              </div>
            </div>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="copdChronic"
                  aria-describedby="copdChronic"
                  name="copdChronic"
                  v-model="formData.copdChronic"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="copdChronic"
                  class="font-medium text-gray-700"
                  >COPD</label
                >
              </div>
            </div>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="cardiacChronic"
                  aria-describedby="cardiacChronic"
                  name="cardiacChronic"
                  v-model="formData.cardiacChronic"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="cardiacChronic"
                  class="font-medium text-gray-700"
                  >Cardiac</label
                >
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="prosthesisChronic"
                  aria-describedby="prosthesisChronic"
                  name="prosthesisChronic"
                  v-model="formData.prosthesisChronic"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="prosthesisChronic"
                  class="font-medium text-gray-700"
                  >Prosthesis</label
                >
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  id="arthrithisChronic"
                  aria-describedby="arthrithisChronic"
                  name="arthrithisChronic"
                  v-model="formData.arthrithisChronic"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="arthrithisChronic"
                  class="font-medium text-gray-700"
                  >Arthritis</label
                >
              </div>
            </div>
          </fieldset>
        </div>

        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Operational Information
          </h3>
        </div>

        <div class="sm:col-span-3">
          <v-date-picker
            color="teal"
            v-model="formData.datePerformed"
            :masks="masks"
            class=""
          >
            <template v-slot="{ inputValue, togglePopover }">
              <label
                for="employment_start_date"
                class="block mb-1 text-sm font-medium text-stone-400"
                >Date Performed</label
              >
              <input
                class="w-full px-2 py-2 font-semibold border rounded-sm shadow-sm border-stone-300 focus:border-stone-500 focus:outline-none focus:ring-1 focus:ring-stone-500"
                :value="inputValue"
                @click="togglePopover"
              />
            </template>
          </v-date-picker>
          <div
            class=""
            v-for="error of v$.datePerformed.$errors"
            :key="error.$uid"
          >
            <div class="text-red-700">{{ error.$message }}</div>
          </div>
        </div>

        <div class="sm:col-span-3">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Performed By</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              v-model="formData.performedBy"
              @blur="v$.performedBy.$touch"
              placeholder=""
            />
            <div
              class=""
              v-for="error of v$.performedBy.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-4">
          <div class="mt-1">
            <Listbox as="div" v-model="selectedClinic">
              <ListboxLabel
                class="block text-sm font-medium text-stone-400"
              >
                Clinic Attended*
              </ListboxLabel>
              <div class="relative mt-1">
                <ListboxButton
                  class="relative w-full py-2 pl-3 pr-10 text-left bg-white border border-gray-300 rounded-sm shadow-sm cursor-default focus:outline-none focus:ring-1 focus:ring-stone-500 focus:border-stone-300 sm:text-sm"
                >
                  <span class="inline-flex w-full truncate">
                    <span class="truncate">{{
                      selectedClinic.clinicName
                    }}</span></span
                  >
                  <span
                    class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"
                  >
                    <ChevronUpDownIcon
                      class="w-5 h-5 text-gray-400"
                      aria-hidden="true"
                    />
                  </span>
                </ListboxButton>

                <transition
                  leave-active-class="transition duration-100 ease-in"
                  leave-from-class="opacity-100"
                  leave-to-class="opacity-0"
                >
                  <ListboxOptions
                    class="absolute z-10 w-full py-1 mt-1 overflow-auto text-base bg-white rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
                  >
                    <ListboxOption
                      as="template"
                      v-for="clinic in clinics"
                      :key="clinic.id"
                      :value="clinic"
                      v-slot="{ active, selected }"
                    >
                      <li
                        :class="[
                          active
                            ? 'text-white bg-stone-600'
                            : 'text-gray-900',
                          'cursor-default select-none relative py-2 pl-3 pr-9',
                        ]"
                      >
                        <div class="flex">
                          <span
                            :class="[
                              selected
                                ? 'font-semibold'
                                : 'font-normal',
                              'truncate',
                            ]"
                          >
                            {{ clinic.clinicName }}
                          </span>
                          <span
                            :class="[
                              active
                                ? 'text-stone-200'
                                : 'text-gray-500',
                              'ml-2 truncate',
                            ]"
                          >
                          </span>
                        </div>

                        <span
                          v-if="selected"
                          :class="[
                            active ? 'text-white' : 'text-stone-600',
                            'absolute inset-y-0 right-0 flex items-center pr-4',
                          ]"
                        >
                          <CheckIcon
                            class="w-5 h-5"
                            aria-hidden="true"
                          />
                        </span>
                      </li>
                    </ListboxOption>
                  </ListboxOptions>
                </transition>
              </div>
            </Listbox>
            <div
              class=""
              v-for="error of v$.clinicId.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>
        <div class="sm:col-span-5">
          <label
            for="description"
            class="block text-sm font-medium text-stone-400"
            >Note</label
          >
          <div class="mt-1">
            <textarea
              autocomplete="off"
              row="4"
              type="text"
              v-model="formData.note"
              name="about"
              id="about"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800 placeholder:text-slate-300 placeholder:font-normal"
              placeholder=""
            ></textarea>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import {
  ArrowSmallLeftIcon,
  CheckIcon,
  ChevronUpDownIcon,
} from "@heroicons/vue/24/solid";
import gql from "graphql-tag";
import { useQuery, useMutation } from "@vue/apollo-composable";
import { useRoute, useRouter } from "vue-router";
import useVuelidate from "@vuelidate/core";
import { required, helpers, integer } from "@vuelidate/validators";
import { ref, reactive, watch, computed } from "vue";

const route = useRoute();

const GET_PATIENT = gql`
  query getPatient($id: ID!) {
    patient(id: $id) {
      id
      fullName
      identificationNumber
    }
  }
`;

const { result: patientResult } = useQuery(
  GET_PATIENT,
  { id: route.params.patient_id },
  {
    fetchPolicy: "cache-and-network",
  },
);

const patient = computed(() => {
  return patientResult.value?.patient ?? "Patient";
});

const CREATE_PHYSICAL = gql`
  mutation createPhysical($input: PhysicalInput!) {
    createPhysical(input: $input) {
      physical {
        id
      }
      success
      errors {
        path
        message
      }
    }
  }
`;

const {
  mutate: sendForm,
  onDone,
  loading: sendFormLoading,
} = useMutation(CREATE_PHYSICAL, () => ({
  variables: {
    input: {
      ...formData,
    },
  },
}));

const rules = {
  weight: {
    required: helpers.withMessage("Weight is required.", required),
    integer: helpers.withMessage(
      "Weight can only be an integer",
      integer,
    ),
  },
  height: {
    required: helpers.withMessage("Height is required.", required),
    integer: helpers.withMessage(
      "Height can only be an integer",
      integer,
    ),
  },
  bloodPressure: {
    required: helpers.withMessage(
      "Blood Pressure is required.",
      required,
    ),
  },
  pulse: {
    required: helpers.withMessage("Pulse is required.", required),
  },
  bloodSugar: {
    required: helpers.withMessage(
      "Blood Sugar is required.",
      required,
    ),
  },
  clinicId: {
    required: helpers.withMessage(
      "Clinic information is required.",
      required,
    ),
  },
  datePerformed: {
    required: helpers.withMessage(
      "Date performed is required.",
      required,
    ),
  },
  generalAppearance: {
    required: helpers.withMessage(
      "Gen. Appearance performed is required.",
      required,
    ),
  },
  performedBy: {
    required: helpers.withMessage(
      " Performed by is required.",
      required,
    ),
  },
};

const formData = reactive({
  patientId: parseInt(route.params.patient_id),

  height: "",
  weight: "",
  bloodPressure: "",
  bloodSugar: "",
  pulse: "",

  urineTest: false,
  nadTest: false,
  leucocytesTest: false,
  nitriteTest: false,
  bloodTest: false,
  proteinTest: false,
  glucoseTest: false,

  generalAppearance: "",

  peripheralExam: false,
  skinExam: false,
  entExam: false,
  cvsExam: false,
  chestExam: false,
  gastroExam: false,
  urinaryExam: false,
  musculoExam: false,
  cnsExam: false,
  endocrineExam: false,
  glucoseExam: false,

  hypertensionChronic: false,
  asthmaChronic: false,
  epilepsyChronic: false,
  mentalChronic: false,
  obesityChronic: false,
  diabetesChronic: false,
  drugChronic: false,
  thyroidChronic: false,
  copdChronic: false,
  cardiacChronic: false,
  prosthesisChronic: false,
  arthrithisChronic: false,

  note: "",
  performedBy: "",
  datePerformed: "",
  clinicId: "",
  status: "draft",
});

const v$ = useVuelidate(rules, formData);

const submitForm = async () => {
  const isFormCorrect = await v$.value.$validate();
  console.log(formData);
  if (!isFormCorrect) return;
  sendForm();
};

const router = useRouter();

onDone((result) => {
  if (result.data.createPhysical.success == true) {
    router.push({
      name: "physicals",
      params: { id: route.params.patient_id },
    });
  } else
    console.log("From Error:", result.data?.createPhysical.success);
});
const selectedClinic = ref("");

watch(
  () => selectedClinic.value,
  (outcome) => {
    formData.clinicId = parseInt(outcome.id);
  },
);

const CLINICS = gql`
  query listClinics($id: ID!) {
    clinics(organisationId: $id) {
      id
      clinicName
    }
  }
`;

const masks = {
  input: "YYYY/MM/DD",
  mode: "date",
  data: ["L", "YYYY-MM-DD", "YYYY/MM/DD"],
};

const { result: clinicResult } = useQuery(
  CLINICS,
  { id: 1 },
  { fetchPolicy: "cache-and-network" },
);

const clinics = computed(() => clinicResult?.value.clinics ?? []);
</script>
