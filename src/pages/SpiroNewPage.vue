<template>
  <q-page padding>
    <form @submit.prevent="submitForm">
      <PageHeading>
        <template #navigation>
          <div class="flex items-center text-gray-500 text-uppercase">
            <router-link
              class="flex"
              :to="{
                name: 'spiros',
                params: { patient_id: route.params.patient_id },
              }"
            >
              <ArrowSmallLeftIcon class="w-5 h-5" />
              Back
            </router-link>
          </div>
        </template>
        <template #heading>
          <div class="capitalize">New Spirometry assessment</div>
        </template>

        <template #buttons>
          <q-btn
            flat
            class="text-white bg-teal-700 text-bold"
            padding="md"
            no-caps
            :loading="sendFormLoading"
            type="submit"
          >
            <template v-slot:loading>
              <q-spinner-ios />
            </template>
            <CheckIcon class="w-5 h-5 mr-1 font-bold" />
            <div class="text-sm">Save</div>
          </q-btn>
        </template>
      </PageHeading>
    </form>

    <div
      class="flex flex-col p-4 rounded-sm shadow md:mt-8 bg-stone-50"
    >
      <div class="max-w-4xl">
        <div class="grid grid-cols-2 gap-4">
          <div class="flex flex-col">
            <div
              class="mb-1 text-xs font-bold uppercase text-stone-400"
            >
              Patient fullname
            </div>
            <div class="font-bold">
              {{ patient.fullName }}
            </div>
          </div>
          <div class="flex flex-col">
            <div
              class="mb-1 text-xs font-bold uppercase text-stone-400"
            >
              ID Number
            </div>
            <div class="font-bold">
              {{ patient.identificationNumber }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-2xl">
      <div
        class="grid grid-cols-1 mt-6 gap-x-4 gap-y-6 sm:grid-cols-6"
      >
        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Spirometry Information
          </h3>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Report Name*</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              v-model="formData.name"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <div
              class=""
              v-for="error of v$.name.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="note"
            class="block text-sm font-medium text-stone-400"
            >Note</label
          >
          <div class="mt-1">
            <textarea
              autocomplete="off"
              row="4"
              type="text"
              name="about"
              id="about"
              v-model="formData.note"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800 placeholder:text-slate-300 placeholder:font-normal"
              placeholder=""
            ></textarea>
          </div>
        </div>

        <div class="sm:col-span-3">
          <v-date-picker
            color="teal"
            v-model="formData.datePerformed"
            :masks="masks"
            class=""
          >
            <template v-slot="{ inputValue, togglePopover }">
              <label
                for="employment_start_date"
                class="block mb-1 text-sm font-medium text-stone-400"
                >Date Performed*</label
              >
              <input
                class="w-full px-2 py-2 font-semibold border rounded-sm shadow-sm border-stone-300 focus:border-stone-500 focus:outline-none focus:ring-1 focus:ring-stone-500"
                :value="inputValue"
                @click="togglePopover"
              />
            </template>
          </v-date-picker>
          <div
            class=""
            v-for="error of v$.datePerformed.$errors"
            :key="error.$uid"
          >
            <div class="text-red-700">{{ error.$message }}</div>
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="result"
            class="block text-sm font-medium text-stone-400"
          >
            Result*</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              @blur="v$.result.$touch"
              v-model="formData.result"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />

            <div
              class=""
              v-for="error of v$.result.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Operational Information
          </h3>
        </div>

        <div class="sm:col-span-3">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Performed By</label
          >
          <div class="mt-1">
            <input
              v-model="formData.performedBy"
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
          </div>
        </div>

        <div class="sm:col-span-3">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            System Used</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              v-model="formData.systemUsed"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
          </div>
        </div>

        <div class="sm:col-span-4">
          <div class="mt-1">
            <Listbox as="div" v-model="selectedClinic">
              <ListboxLabel
                class="block text-sm font-medium text-stone-400"
              >
                Clinic Attended*
              </ListboxLabel>
              <div class="relative mt-1">
                <ListboxButton
                  class="relative w-full py-2 pl-3 pr-10 text-left bg-white border border-gray-300 rounded-sm shadow-sm cursor-default focus:outline-none focus:ring-1 focus:ring-stone-500 focus:border-stone-300 sm:text-sm"
                >
                  <span class="inline-flex w-full truncate">
                    <span class="truncate">{{
                      selectedClinic.clinicName
                    }}</span></span
                  >
                  <span
                    class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"
                  >
                    <ChevronUpDownIcon
                      class="w-5 h-5 text-gray-400"
                      aria-hidden="true"
                    />
                  </span>
                </ListboxButton>

                <transition
                  leave-active-class="transition duration-100 ease-in"
                  leave-from-class="opacity-100"
                  leave-to-class="opacity-0"
                >
                  <ListboxOptions
                    class="absolute z-10 w-full py-1 mt-1 overflow-auto text-base bg-white rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
                  >
                    <ListboxOption
                      as="template"
                      v-for="clinic in clinics"
                      :key="clinic.id"
                      :value="clinic"
                      v-slot="{ active, selected }"
                    >
                      <li
                        :class="[
                          active
                            ? 'text-white bg-stone-600'
                            : 'text-gray-900',
                          'cursor-default select-none relative py-2 pl-3 pr-9',
                        ]"
                      >
                        <div class="flex">
                          <span
                            :class="[
                              selected
                                ? 'font-semibold'
                                : 'font-normal',
                              'truncate',
                            ]"
                          >
                            {{ clinic.clinicName }}
                          </span>
                          <span
                            :class="[
                              active
                                ? 'text-stone-200'
                                : 'text-gray-500',
                              'ml-2 truncate',
                            ]"
                          >
                          </span>
                        </div>

                        <span
                          v-if="selected"
                          :class="[
                            active ? 'text-white' : 'text-stone-600',
                            'absolute inset-y-0 right-0 flex items-center pr-4',
                          ]"
                        >
                          <CheckIcon
                            class="w-5 h-5"
                            aria-hidden="true"
                          />
                        </span>
                      </li>
                    </ListboxOption>
                  </ListboxOptions>
                </transition>
              </div>
            </Listbox>
            <div
              class=""
              v-for="error of v$.clinicId.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Attachment(s)*
          </h3>
          <div
            class=""
            v-for="error of v$.attachments.$errors"
            :key="error.$uid"
          >
            <div class="text-red-700">{{ error.$message }}</div>
          </div>
        </div>

        <div class="sm:col-span-3 h-63">
          <file-pond
            credits="false"
            allow-multiple="true"
            store-as-file="true"
            allow-process="false"
            instant-upload="false"
            v-on:addfile="addFile"
            v-on:removefile="removeFile"
            accepted-file-types="image/jpeg, image/png, application/pdf"
          />
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import vueFilePond from "vue-filepond";
import {
  ArrowSmallLeftIcon,
  CheckIcon,
  ChevronUpDownIcon,
} from "@heroicons/vue/24/solid";
import gql from "graphql-tag";
import { useQuery, useMutation } from "@vue/apollo-composable";
import { useRoute, useRouter } from "vue-router";
import useVuelidate from "@vuelidate/core";
import { required, helpers } from "@vuelidate/validators";
import "filepond/dist/filepond.min.css";
import { ref, reactive, watch, computed } from "vue";

import FilePondPluginFileValidateType from "filepond-plugin-file-validate-type";

const route = useRoute();
const FilePond = vueFilePond(FilePondPluginFileValidateType);

const masks = {
  input: "YYYY/MM/DD",
  mode: "date",
  data: ["L", "YYYY-MM-DD", "YYYY/MM/DD"],
};

const GET_PATIENT = gql`
  query getPatient($id: ID!) {
    patient(id: $id) {
      id
      fullName
      identificationNumber
    }
  }
`;

const { result: patientResult } = useQuery(
  GET_PATIENT,
  { id: route.params.patient_id },
  {
    fetchPolicy: "cache-and-network",
  },
);

const patient = computed(() => {
  return patientResult.value?.patient ?? "Patient";
});

const CREATE_SPIRO = gql`
  mutation createSpiro($input: SpiroInput!) {
    createSpiro(input: $input) {
      success
      spiro {
        name
        note
        id
      }
    }
  }
`;

const {
  mutate: sendForm,
  onDone,
  loading: sendFormLoading,
} = useMutation(CREATE_SPIRO, () => ({
  variables: {
    id: route.params.patient_id,
    input: {
      ...formData,
    },
  },
}));

const rules = {
  name: {
    required: helpers.withMessage(
      "Document title is required.",
      required,
    ),
  },
  result: {
    required: helpers.withMessage("Result is required.", required),
  },
  datePerformed: {
    required: helpers.withMessage(
      "Date performed is required.",
      required,
    ),
  },
  clinicId: {
    required: helpers.withMessage(
      "Date performed is required.",
      required,
    ),
  },
  attachments: {
    required: helpers.withMessage(
      "Attachments can't be empty.",
      required,
    ),
  },
};

const formData = reactive({
  name: "",
  note: "",
  attachments: [],
  clinicId: "",
  systemUsed: "",
  result: "",
  performedBy: "",
  datePerformed: "",
  patientId: parseInt(route.params.patient_id),
});

const addFile = (error, file) => {
  formData.attachments = [...formData.attachments, file.file];
};
const removeFile = (error, file) => {
  const result = formData.attachments.filter((item) => {
    return item.id != file.id;
  });

  formData.attachments = [...result];
};
const v$ = useVuelidate(rules, formData);

const submitForm = async () => {
  const isFormCorrect = await v$.value.$validate();
  console.log(formData);
  if (!isFormCorrect) return;
  sendForm();
};

const router = useRouter();

onDone((result) => {
  if (result.data.createSpiro.success == true) {
    router.push({
      name: "spiros",
      params: { id: route.params.patient_id },
    });
  } else console.log("From Error:", result.data?.createSpiro.success);
});

const selectedClinic = ref("");

watch(
  () => selectedClinic.value,
  (outcome) => {
    formData.clinicId = parseInt(outcome.id);
  },
);

const CLINICS = gql`
  query listClinics($id: ID!) {
    clinics(organisationId: $id) {
      id
      clinicName
    }
  }
`;

const { result: clinicResult } = useQuery(
  CLINICS,
  { id: 1 },
  { fetchPolicy: "cache-and-network" },
);

const clinics = computed(() => clinicResult?.value.clinics ?? []);
</script>
