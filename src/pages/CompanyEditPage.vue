<template>
  <q-page padding>
    <form @submit.prevent="submitForm">
      <PageHeading>
        <template #navigation>
          <div class="flex items-center text-gray-500 text-uppercase">
            <router-link class="flex" to="/companies">
              <ArrowSmallLeftIcon class="w-5 h-5" />
              Back
            </router-link>
          </div>
        </template>
        <template #heading>
          <div class="capitalize">
            {{ company.name }}
          </div>
        </template>

        <template #buttons>
          <q-btn
            flat
            class="text-white bg-teal-700 text-bold"
            padding="md"
            no-caps
            :loading="sendFormLoading"
            type="submit"
          >
            <template v-slot:loading>
              <q-spinner-ios />
            </template>
            <CheckIcon class="w-5 h-5 mr-1 font-bold" />
            <div class="text-sm">Save</div>
          </q-btn>
        </template>
      </PageHeading>
    </form>

    <div class="max-w-2xl">
      <div
        class="grid grid-cols-1 mt-6 gap-x-4 gap-y-6 sm:grid-cols-6"
      >
        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Company Information
          </h3>
        </div>

        <div class="sm:col-span-4">
          <label
            for="identification"
            class="block text-sm font-medium text-stone-400"
          >
            Company Name*</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              v-model="formData.name"
              @blur="v$.name.$touch"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <div
              class=""
              v-for="error of v$.name.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="firstName"
            class="block text-sm font-medium text-stone-400"
            >Industry</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="industry"
              id="industry"
              v-model="formData.industrySector"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
          </div>
        </div>

        <div class="sm:col-span-3">
          <label
            for="lastName"
            class="block text-sm font-medium text-stone-400"
            >About</label
          >
          <div class="mt-1">
            <textarea
              autocomplete="off"
              row="4"
              type="text"
              name="about"
              id="about"
              v-model="formData.about"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800 placeholder:text-slate-300 placeholder:font-normal"
              placeholder=""
            ></textarea>
          </div>
        </div>

        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Location
          </h3>
        </div>

        <div class="sm:col-span-5">
          <label
            for="street_address"
            class="block text-sm font-medium text-stone-400"
            >Street Address*</label
          >
          <div class="mt-1">
            <input
              type="text"
              name="street_address"
              id="street_address"
              v-model="formData.streetAddress"
              autocomplete="new-street-address"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="suburb"
            class="block text-sm font-medium text-stone-400"
            >Suburb*</label
          >
          <div class="mt-1">
            <input
              type="text"
              name="suburb"
              id="suburb"
              v-model="formData.suburb"
              autocomplete="new-suburb"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
          </div>
        </div>
        <div class="sm:col-span-3">
          <label
            for="city"
            class="block text-sm font-medium text-stone-400"
            >City*</label
          >
          <div class="mt-1">
            <input
              type="tel"
              name="city"
              id="city"
              autocomplete="new-city"
              v-model="formData.city"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
          </div>
        </div>

        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Contact Information
          </h3>
        </div>

        <div class="sm:col-span-3">
          <label
            for="phoneNumber"
            class="block text-sm font-medium text-stone-400"
            >Phone Number*</label
          >
          <div class="mt-1">
            <input
              type="text"
              @blur="v$.phoneNumber.$touch"
              v-model="formData.phoneNumber"
              name="phoneNumber"
              id="phoneNumber"
              autocomplete="off"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
              v-mask="'(###) ###-####'"
            />
            <div
              class=""
              v-for="error of v$.phoneNumber.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-3">
          <label
            for="email"
            class="block text-sm font-medium text-stone-400"
            >Email</label
          >
          <div class="mt-1">
            <input
              type="email"
              name="email"
              id="email"
              autocomplete="off"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              v-model="formData.email"
              placeholder=""
            />
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import {
  ArrowSmallLeftIcon,
  CheckIcon,
} from "@heroicons/vue/24/solid";
import gql from "graphql-tag";
import { useQuery, useMutation } from "@vue/apollo-composable";
import { useRoute, useRouter } from "vue-router";
import { watch, reactive, ref, computed } from "vue";
import useVuelidate from "@vuelidate/core";
import { required, helpers } from "@vuelidate/validators";

const route = useRoute();

const GET_COMPANY = gql`
  query getCompany($id: ID!) {
    company(id: $id) {
      id
      name
      suburb
      email
      industrySector
      about
      city
      phoneNumber
      streetAddress
    }
  }
`;

const UPDATE_COMPANY = gql`
  mutation updateCompany($id: ID!, $input: CompanyInput!) {
    updateCompany(id: $id, input: $input) {
      company {
        name
      }
      success
      errors {
        path
        message
      }
    }
  }
`;

const { result } = useQuery(
  GET_COMPANY,
  {
    id: route.params.id,
  },
  {
    fetchPolicy: "cache-and-network",
  },
);

const company = computed(() => {
  return result.value?.company ?? "Company";
});

const query = ref("");

const {
  mutate: sendForm,
  onDone,
  onError,
  loading: sendFormLoading,
} = useMutation(UPDATE_COMPANY, () => ({
  variables: {
    id: route.params.id,
    input: {
      ...formData,
    },
  },
}));

const rules = {
  name: {
    required: helpers.withMessage(
      "Company name is required.",
      required,
    ),
  },
  phoneNumber: {
    required: helpers.withMessage(
      "Company contact number is required.",
      required,
    ),
  },
};

const formData = reactive({
  name: "",
  industrySector: "",
  about: "",
  streetAddress: "",
  suburb: "",
  city: "",
  phoneNumber: "",
  email: "",
});

watch(
  () => company.value,
  (outcome) => {
    formData.name = outcome.name;
    formData.industrySector = outcome.industrySector;
    formData.about = outcome.about;
    formData.streetAddress = outcome.streetAddress;
    formData.suburb = outcome.suburb;
    formData.city = outcome.city;
    formData.phoneNumber = outcome.phoneNumber;
    formData.email = outcome.email;
  },
);

const v$ = useVuelidate(rules, formData);

const submitForm = async () => {
  const isFormCorrect = await v$.value.$validate();
  if (!isFormCorrect) return;
  sendForm();
};

const router = useRouter();

onDone((result) => {
  if (result.data.updateCompany.success == true) {
    router.push(`/companies/${route.params.id}`);
  } else {
    console.log("From Error:", result.data?.updateCompany.success);
  }
});
</script>
