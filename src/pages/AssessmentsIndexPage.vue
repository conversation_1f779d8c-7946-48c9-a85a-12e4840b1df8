<template>
  <q-page padding>
    <div class="max-w-5xl">
      <PageHeading class="pb-6 border-stone-400">
        <template #navigation>
          <div class="flex items-center text-gray-500 text-uppercase">
            <ArrowSmallLeftIcon class="w-5 h-5" />
            <router-link
              :to="{
                name: 'show_patient',
                params: { id: route.params.patient_id },
              }"
              >Back</router-link
            >
          </div>
        </template>

        <template #heading> Assessment List </template>

        <template #description>
          Select the test you would like to perform
        </template>
      </PageHeading>

      <div
        class="overflow-hidden divide-y rounded-lg shadow sm:gap-py bg-stone-50 divide-stone-200 sm:divide-y-1 sm:grid sm:grid-cols-2 sm:gap-px"
      >
        <div
          class="relative p-4 bg-white rounded-tl-lg rounded-tr-lg sm:rounded-tr-none group focus-within:ring-2 focus-within:ring-inset focus-within:ring-stone-500"
        >
          <div>
            <span
              class="inline-flex p-2 text-teal-700 rounded-lg bg-teal-50 ring-4 ring-white"
            >
              <SpeakerWaveIcon class="w-6 h-6" aria-hidden="true" />
            </span>
          </div>
          <div class="mt-4">
            <h3 class="mb-2 text-lg font-medium">
              <router-link
                class="focus:outline-none"
                :to="{
                  name: 'audios',
                  params: { patient_id: route.params.patient_id },
                }"
              >
                <!-- Extend touch target to entire panel -->
                <span class="absolute inset-0" aria-hidden="true" />
                Audiometry
              </router-link>
            </h3>
          </div>
          <span
            class="absolute text-gray-300 pointer-events-none top-6 right-6 group-hover:text-gray-400"
            aria-hidden="true"
          >
            <ArrowUpRightIcon class="w-6 h-6" />
          </span>
        </div>

        <div
          class="relative p-4 bg-white rounded-tl-lg rounded-tr-lg sm:rounded-tr-none group focus-within:ring-2 focus-within:ring-inset focus-within:ring-stone-500"
        >
          <div>
            <span
              class="inline-flex p-2 text-purple-700 rounded-lg bg-purple-50 ring-4 ring-white"
            >
              <MicrophoneIcon class="w-6 h-6" aria-hidden="true" />
            </span>
          </div>
          <div class="mt-4">
            <h3 class="mb-2 text-lg font-medium">
              <router-link
                class="focus:outline-none"
                :to="{
                  name: 'spiros',
                  params: { patient_id: route.params.patient_id },
                }"
              >
                <!-- Extend touch target to entire panel -->
                <span class="absolute inset-0" aria-hidden="true" />
                Spirometry
              </router-link>
            </h3>
          </div>
          <span
            class="absolute text-gray-300 pointer-events-none top-6 right-6 group-hover:text-gray-400"
            aria-hidden="true"
          >
            <ArrowUpRightIcon class="w-6 h-6" />
          </span>
        </div>

        <div
          class="relative p-4 bg-white rounded-tl-lg rounded-tr-lg sm:rounded-tr-none group focus-within:ring-2 focus-within:ring-inset focus-within:ring-stone-500"
        >
          <div>
            <span
              class="inline-flex p-2 rounded-lg bg-sky-50 text-sky-700 ring-4 ring-white"
            >
              <BeakerIcon class="w-6 h-6" aria-hidden="true" />
            </span>
          </div>
          <div class="mt-4">
            <h3 class="mb-2 text-lg font-medium">
              <router-link
                class="focus:outline-none"
                :to="{
                  name: 'drug_screenings',
                  params: { patient_id: route.params.patient_id },
                }"
              >
                <!-- Extend touch target to entire panel -->
                <span class="absolute inset-0" aria-hidden="true" />
                Drug Screening
              </router-link>
            </h3>
          </div>
          <span
            class="absolute text-gray-300 pointer-events-none top-6 right-6 group-hover:text-gray-400"
            aria-hidden="true"
          >
            <ArrowUpRightIcon class="w-6 h-6" />
          </span>
        </div>

        <div
          class="relative p-4 bg-white rounded-tl-lg rounded-tr-lg sm:rounded-tr-none group focus-within:ring-2 focus-within:ring-inset focus-within:ring-stone-500"
        >
          <div>
            <span
              class="inline-flex p-2 text-yellow-700 rounded-lg bg-yellow-50 ring-4 ring-white"
            >
              <MicrophoneIcon class="w-6 h-6" aria-hidden="true" />
            </span>
          </div>
          <div class="mt-4">
            <h3 class="mb-2 text-lg font-medium">
              <router-link
                class="focus:outline-none"
                :to="{
                  name: 'vision_screenings',
                  params: { patient_id: route.params.patient_id },
                }"
              >
                <!-- Extend touch target to entire panel -->
                <span class="absolute inset-0" aria-hidden="true" />
                Vision Screening
              </router-link>
            </h3>
          </div>
          <span
            class="absolute text-gray-300 pointer-events-none top-6 right-6 group-hover:text-gray-400"
            aria-hidden="true"
          >
            <ArrowUpRightIcon class="w-6 h-6" />
          </span>
        </div>

        <div
          class="relative p-4 bg-white rounded-tl-lg rounded-tr-lg sm:rounded-tr-none group focus-within:ring-2 focus-within:ring-inset focus-within:ring-stone-500"
        >
          <div>
            <span
              class="inline-flex p-2 rounded-lg bg-rose-50 text-rose-700 ring-4 ring-white"
            >
              <PaperClipIcon class="w-6 h-6" aria-hidden="true" />
            </span>
          </div>
          <div class="mt-4">
            <h3 class="mb-2 text-lg font-medium">
              <router-link
                class="focus:outline-none"
                :to="{
                  name: 'new_annexure_three',
                  params: { patient_id: route.params.patient_id },
                }"
              >
                <!-- Extend touch target to entire panel -->
                <span class="absolute inset-0" aria-hidden="true" />
                Annexure Three (WIP)
              </router-link>
            </h3>
          </div>
          <span
            class="absolute text-gray-300 pointer-events-none top-6 right-6 group-hover:text-gray-400"
            aria-hidden="true"
          >
            <ArrowUpRightIcon class="w-6 h-6" />
          </span>
        </div>

        <div
          class="relative p-4 bg-white rounded-tl-lg rounded-tr-lg sm:rounded-tr-none group focus-within:ring-2 focus-within:ring-inset focus-within:ring-stone-500"
        >
          <div>
            <span
              class="inline-flex p-2 rounded-lg bg-emerald-50 text-emerald-700 ring-4 ring-white"
            >
              <DocumentIcon class="w-6 h-6" aria-hidden="true" />
            </span>
          </div>
          <div class="mt-4">
            <h3 class="mb-2 text-lg font-medium">
              <router-link
                class="focus:outline-none"
                :to="{
                  name: 'patient_documents',
                  params: { patient_id: route.params.patient_id },
                }"
              >
                <!-- Extend touch target to entire panel -->
                <span class="absolute inset-0" aria-hidden="true" />
                Patient Document
              </router-link>
            </h3>
          </div>
          <span
            class="absolute text-gray-300 pointer-events-none top-6 right-6 group-hover:text-gray-400"
            aria-hidden="true"
          >
            <ArrowUpRightIcon class="w-6 h-6" />
          </span>
        </div>

        <div
          class="relative p-4 bg-white rounded-tl-lg rounded-tr-lg sm:rounded-tr-none group focus-within:ring-2 focus-within:ring-inset focus-within:ring-stone-500"
        >
          <div>
            <span
              class="inline-flex p-2 rounded-lg bg-fuchsia-50 text-fuchsia-700 ring-4 ring-white"
            >
              <PaperClipIcon class="w-6 h-6" aria-hidden="true" />
            </span>
          </div>
          <div class="mt-4">
            <h3 class="mb-2 text-lg font-medium">
              <router-link
                class="focus:outline-none"
                :to="{
                  name: 'physicals',
                  params: { patient_id: route.params.patient_id },
                }"
              >
                <!-- Extend touch target to entire panel -->
                <span class="absolute inset-0" aria-hidden="true" />
                Physical Assessment
              </router-link>
            </h3>
          </div>
          <span
            class="absolute text-gray-300 pointer-events-none top-6 right-6 group-hover:text-gray-400"
            aria-hidden="true"
          >
            <ArrowUpRightIcon class="w-6 h-6" />
          </span>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import {
  AcademicCapIcon,
  CheckBadgeIcon,
  BeakerIcon,
  BanknotesIcon,
  ClockIcon,
  EyeIcon,
  PaperClipIcon,
  ReceiptRefundIcon,
  UsersIcon,
  DocumentIcon,
  HandRaisedIcon,
  MicrophoneIcon,
  ArrowSmallLeftIcon,
  IdentificationIcon,
  SpeakerWaveIcon,
  ArrowUpRightIcon,
} from "@heroicons/vue/24/outline";
import { useRoute } from "vue-router";
const route = useRoute();

const actions = [
  {
    title: "Audiometry",
    href: `/patients/${route.params.patient_id}/audios`,
    icon: SpeakerWaveIcon,
    iconForeground: "text-teal-700",
    iconBackground: "bg-teal-50",
  },
  {
    title: "Spirometry",
    href: `/patients/${route.params.patient_id}/spiros`,
    icon: MicrophoneIcon,
    iconForeground: "text-purple-700",
    iconBackground: "bg-purple-50",
  },
  {
    title: "Drug Screening",
    href: "#",
    icon: BeakerIcon,
    iconForeground: "text-sky-700",
    iconBackground: "bg-sky-50",
  },
  {
    title: "Vision Screening",
    href: "#",
    icon: EyeIcon,
    iconForeground: "text-yellow-700",
    iconBackground: "bg-yellow-50",
  },
  {
    title: "Annexure Three",
    href: "#",
    icon: PaperClipIcon,
    iconForeground: "text-rose-700",
    iconBackground: "bg-rose-50",
  },
  {
    title: "Physical Assessment",
    href: "#",
    icon: IdentificationIcon,
    iconForeground: "text-fuchsia-700",
    iconBackground: "bg-fuchsia-50",
  },
  {
    title: "Referral Letter",
    href: "#",
    icon: ReceiptRefundIcon,
    iconForeground: "text-cyan-700",
    iconBackground: "bg-cyan-50",
  },
  {
    title: "Patient Document",
    href: `/patients/${route.params.patient_id}/patient_documents`,
    icon: DocumentIcon,
    iconForeground: "text-emerald-700",
    iconBackground: "bg-emerald-50",
  },
  {
    title: "Exclusions",
    href: "#",
    icon: HandRaisedIcon,
    iconForeground: "text-amber-700",
    iconBackground: "bg-amber-50",
  },
];
</script>
