<template>
  <q-page padding>
    <PageHeading class="max-w-4xl">
      <template #navigation>
        <div class="flex items-center text-gray-500 text-uppercase">
          <ArrowSmallLeftIcon class="w-5 h-5" />
          <router-link
            :to="{
              name: 'patient_documents',
              params: { patient_id: backUrl },
            }"
            >Back</router-link
          >
        </div>
      </template>
      <template #heading>
        <div class="capitalize">{{ document.name }}</div>
        <!-- <div class="capitalize">[Audio Report] 20221 Vinnie</div> -->
      </template>

      <template #buttons>
        <q-btn
          flat
          class="text-white bg-teal-700 text-bold"
          padding="md"
          no-caps
        >
          <PencilIcon class="w-5 h-5 mr-1 text-bold" />
          <div class="text-sm">Edit Document</div>
        </q-btn>
      </template>
    </PageHeading>

    <div class="max-w-4xl">
      <div
        class="flex flex-col p-4 rounded-sm shadow md:mt-8 bg-stone-50"
      >
        <div class="max-w-4xl">
          <div class="grid grid-cols-2 gap-4">
            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-400"
              >
                Patient
              </div>
              <div class="font-bold">
                Vinnie Man
                <!-- {{ medical.patient?.fullName }} -->
              </div>
            </div>
            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-400"
              >
                Date Uploaded
              </div>
              <div class="font-bold">
                {{
                  date.formatDate(
                    document.createdAt,
                    "YYYY-MM-DD - HH:MM",
                  )
                }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="max-w-4xl">
        <div
          class="flex flex-col p-4 mt-8 rounded-sm shadow bg-stone-50"
        >
          <div class="sm:col-span-4">
            <h3 class="text-lg font-semibold leading-6 text-gray-900">
              Description
            </h3>
            <p class="text-sm leading-5 text-gray-500">
              {{
                document.description
                  ? document.description
                  : "No Description"
              }}
            </p>
          </div>
        </div>
      </div>

      <div class="max-w-md">
        <div class="mt-10">
          <div class="" v-if="reportCount > 0">
            <div class="sm:flex sm:items-center">
              <div class="sm:flex-auto">
                <h1
                  class="flex items-center text-xl font-semibold text-gray-900"
                >
                  Attachments ({{ reportCount }})
                </h1>
                <p class="mt-2 text-sm text-gray-700">
                  A list of generated reports for this medical
                </p>
              </div>
            </div>
            <div class="flex flex-col mt-8">
              <div
                class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"
              >
                <div
                  class="inline-block min-w-full px-6 py-2 align-middle lg:px-8"
                >
                  <div
                    class="overflow-hidden rounded-sm shadow ring-1 ring-black ring-opacity-5"
                  >
                    <table
                      class="min-w-full divide-y divide-gray-300"
                    >
                      <thead class="bg-stone-50">
                        <tr>
                          <th
                            scope="col"
                            class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-4"
                          >
                            File Attachment
                          </th>
                        </tr>
                      </thead>
                      <tbody
                        class="bg-white divide-y divide-gray-200"
                      >
                        <tr
                          v-for="report in attachments"
                          :key="report.id"
                        >
                          <td
                            class="flex justify-between py-2 pl-6 text-sm text-gray-500 whitespace-nowrap"
                            ref="report.id"
                          >
                            <span class="font-medium">
                              <a :href="report.url" target="_blank">
                                {{ report.fileName }}
                              </a>
                            </span>
                            <span class="mr-4">
                              <TrashIcon
                                @click="removeAttachment(report.id)"
                                class="w-5 h-5 text-stone-400 hover:text-teal-800"
                              />
                            </span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="" v-else>
            <div class="sm:flex sm:items-center">
              <div class="sm:flex-auto">
                <h1
                  class="flex items-center text-xl font-semibold text-gray-900"
                >
                  Attachments
                </h1>
                <p class="mt-2 text-sm text-gray-700">
                  A list of attachments for this document
                </p>
              </div>
            </div>
            <div class="flex flex-col mt-8">
              <div
                class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"
              >
                <div
                  class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8"
                >
                  <div
                    class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg"
                  >
                    <table
                      class="min-w-full divide-y divide-gray-300"
                    >
                      <thead class="bg-stone-50">
                        <tr>
                          <th
                            scope="col"
                            class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"
                          ></th>
                        </tr>
                      </thead>
                      <tbody
                        class="flex items-center justify-center p-4 bg-white divide-y divide-gray-200"
                      >
                        <tr class="">
                          <EmptyState
                            class="flex justify-center p-4"
                          />
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { useRoute, useRouter } from "vue-router";
import { useQuery, useMutation } from "@vue/apollo-composable";
import gql from "graphql-tag";
import { computed, ref, reactive } from "vue";
import { date } from "quasar";
import { useDebounceFn } from "@vueuse/core";
import {
  PencilIcon,
  ArrowSmallLeftIcon,
  ArrowDownTrayIcon,
  ArrowPathIcon,
  TrashIcon,
} from "@heroicons/vue/24/outline";

const GET_PATIENT_DOCUMENT = gql`
  query patientDocumentById($id: ID!) {
    patientDocument(id: $id) {
      id
      name
      patientId
      description
      createdAt
      attachments {
        id
        fileName
        url
      }
    }
  }
`;

const REMOVE_ATTACHMENT = gql`
  mutation deletePatientDocument($id: ID!) {
    deletePatientDocumentAttachment(id: $id) {
      success
      errors {
        path
        message
      }
    }
  }
`;

let itemToDelete = ref();

const { mutate } = useMutation(REMOVE_ATTACHMENT, () => ({
  variables: {
    id: itemToDelete,
  },
}));

const debouncedFetch = useDebounceFn(
  () => {
    refetch();
  },
  3000,
  { maxWait: 6000 },
);

function removeAttachment(arg) {
  itemToDelete = arg;
  mutate();
  debouncedFetch();
}

// const submitForm = async () => {
//   const isFormCorrect = await v$.value.$validate();
//   if (!isFormCorrect) return;
//   sendForm();
//   setIsOpen(false);
//   v$.value?.$reset();
//   formData.pin = "";
// };

// onDone((result) => {
//   if (result.data.createMedicalReport.success == true) {
//     debouncedFetch();
//   } else {
//     console.log(
//       "From Error:",
//       result.data?.createMedicalReport.success,
//     );
//   }
// });

const route = useRoute();

const { result, refetch } = useQuery(
  GET_PATIENT_DOCUMENT,
  { id: route.params.id },
  { fetchPolicy: "cache-and-network" },
);

const document = computed(() => {
  return result.value?.patientDocument ?? "No Patient Document";
});

const attachments = computed(() => {
  return document.value?.attachments;
});

const backUrl = computed(() => {
  return document.value?.patientId ?? 1;
});

const reportCount = computed(() => {
  return attachments.value?.length ?? 0;
});
</script>
