<template>
  <q-page padding>
    <form @submit.prevent="submitForm">
      <PageHeading>
        <template #navigation>
          <div class="flex items-center text-gray-500 text-uppercase">
            <router-link
              class="flex"
              :to="{
                name: 'assessments',
                params: { patient_id: route.params.patient_id },
              }"
            >
              <ArrowSmallLeftIcon class="w-5 h-5" />
              Back
            </router-link>
          </div>
        </template>
        <template #heading>
          <div class="capitalize">New Patient Document</div>
        </template>

        <template #buttons>
          <q-btn
            flat
            class="text-white bg-teal-700 text-bold"
            padding="md"
            no-caps
            :loading="sendFormLoading"
            type="submit"
          >
            <template v-slot:loading>
              <q-spinner-ios />
            </template>
            <CheckIcon class="w-5 h-5 mr-1 font-bold" />
            <div class="text-sm">Save</div>
          </q-btn>
        </template>
      </PageHeading>
    </form>

    <div class="max-w-2xl">
      <div
        class="grid grid-cols-1 mt-6 gap-x-4 gap-y-6 sm:grid-cols-6"
      >
        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Document Details
          </h3>
        </div>

        <div class="sm:col-span-4">
          <label
            for="identification"
            class="block text-sm font-medium text-stone-400"
          >
            Title*</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              v-model="formData.name"
              @blur="v$.name.$touch"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <div
              class=""
              v-for="error of v$.name.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-3">
          <label
            for="description"
            class="block text-sm font-medium text-stone-400"
            >Description</label
          >
          <div class="mt-1">
            <textarea
              autocomplete="off"
              row="4"
              type="text"
              name="about"
              id="about"
              v-model="formData.description"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800 placeholder:text-slate-300 placeholder:font-normal"
              placeholder=""
            ></textarea>
          </div>
        </div>

        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Document Upload
          </h3>
        </div>

        <div class="sm:col-span-3 h-63">
          <file-pond
            credits="false"
            allow-multiple="true"
            store-as-file="true"
            allow-process="false"
            instant-upload="false"
            v-on:addfile="addFile"
            v-on:removefile="removeFile"
            accepted-file-types="image/jpeg, image/png, application/pdf"
          />
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import vueFilePond from "vue-filepond";
import {
  ArrowSmallLeftIcon,
  CheckIcon,
} from "@heroicons/vue/24/solid";
import gql from "graphql-tag";
import { useMutation } from "@vue/apollo-composable";
import { useRoute, useRouter } from "vue-router";
import { reactive } from "vue";
import useVuelidate from "@vuelidate/core";
import { required, helpers } from "@vuelidate/validators";
import "filepond/dist/filepond.min.css";

import FilePondPluginFileValidateType from "filepond-plugin-file-validate-type";

const route = useRoute();
const FilePond = vueFilePond(FilePondPluginFileValidateType);
const CREATE_PATIENT_DOCUMENT = gql`
  mutation createPatientDocument(
    $id: ID!
    $input: PatientDocumentInput!
  ) {
    createPatientDocument(patientId: $id, input: $input) {
      patientDocument {
        id
        name
        attachments {
          url
          id
        }
      }
      success
      errors {
        path
        message
      }
    }
  }
`;

const {
  mutate: sendForm,
  onDone,
  loading: sendFormLoading,
} = useMutation(CREATE_PATIENT_DOCUMENT, () => ({
  variables: {
    id: route.params.patient_id,
    input: {
      ...formData,
    },
  },
}));

const rules = {
  name: {
    required: helpers.withMessage(
      "Document title is required.",
      required,
    ),
  },
  attachments: {
    required: helpers.withMessage(
      "Attachments can't be empty.",
      required,
    ),
  },
};

const formData = reactive({
  name: "",
  description: "",
  attachments: [],
});

const addFile = (error, file) => {
  formData.attachments = [...formData.attachments, file.file];
};
const removeFile = (error, file) => {
  const result = formData.attachments.filter((item) => {
    return item.id != file.id;
  });

  formData.attachments = [...result];
};
const v$ = useVuelidate(rules, formData);

const submitForm = async () => {
  const isFormCorrect = await v$.value.$validate();
  console.log(formData);
  if (!isFormCorrect) return;
  sendForm();
};

const router = useRouter();

onDone((result) => {
  if (result.data.createPatientDocument.success == true) {
    router.push({
      name: "patient_documents",
      params: { id: route.params.patient_id },
    });
  } else
    console.log(
      "From Error:",
      result.data?.createPatientDocument.success,
    );
});
</script>
