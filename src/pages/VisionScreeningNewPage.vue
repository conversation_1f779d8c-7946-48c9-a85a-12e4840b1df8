<template>
  <q-page padding>
    <form @submit.prevent="submitForm">
      <PageHeading class="max-w-3xl">
        <template #navigation>
          <div class="flex items-center text-gray-500 text-uppercase">
            <router-link
              class="flex"
              :to="{
                name: 'vision_screenings',
                params: { id: route.params.patient_id },
              }"
            >
              <ArrowSmallLeftIcon class="w-5 h-5" />
              Back
            </router-link>
          </div>
        </template>
        <template #heading>
          <div class="capitalize">
            New Vision Screening assessment
          </div>
        </template>

        <template #buttons>
          <q-btn
            flat
            class="text-white bg-teal-700 text-bold"
            padding="md"
            no-caps
            :loading="sendFormLoading"
            type="submit"
          >
            <template v-slot:loading>
              <q-spinner-ios />
            </template>
            <CheckIcon class="w-5 h-5 mr-1 font-bold" />
            <div class="text-sm">Save</div>
          </q-btn>
        </template>
      </PageHeading>
    </form>

    <div
      class="flex flex-col max-w-3xl p-4 rounded-sm shadow md:mt-8 bg-stone-50"
    >
      <div class="max-w-3xl">
        <div class="grid grid-cols-2 gap-4">
          <div class="flex flex-col">
            <div
              class="mb-1 text-xs font-bold uppercase text-stone-400"
            >
              Patient fullname
            </div>
            <div class="font-bold">
              {{ patient.fullName }}
            </div>
          </div>
          <div class="flex flex-col">
            <div
              class="mb-1 text-xs font-bold uppercase text-stone-400"
            >
              ID Number
            </div>
            <div class="font-bold">
              {{ patient.identificationNumber }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-3xl">
      <div
        class="grid grid-cols-1 mt-6 gap-x-4 gap-y-6 sm:grid-cols-6"
      >
        <div class="sm:col-span-6">
          <h3
            class="p-2 text-lg font-semibold leading-6 text-gray-900 bg-stone-100"
          >
            Right Eye
          </h3>
        </div>

        <div class="sm:col-span-3">
          <label class="text-base font-medium text-gray-900"
            >Acuity with glasses / contact lenses</label
          >
          <p class="text-sm leading-5 text-gray-500">
            Indicate the level of acuity below?
          </p>
          <fieldset class="mt-4">
            <legend class="sr-only">Notification method</legend>
            <div class="space-y-4">
              <div
                v-for="notificationMethod in snellenRight"
                :key="notificationMethod.id"
                class="flex items-center"
              >
                <input
                  :value="notificationMethod.title"
                  v-model="formData.snellenRightEye"
                  :id="notificationMethod.id"
                  :name="notificationMethod.id"
                  type="radio"
                  class="w-4 h-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"
                />
                <label
                  :for="notificationMethod.id"
                  class="block ml-3 text-sm font-medium text-gray-700"
                  >{{ notificationMethod.title }}</label
                >
              </div>
            </div>
          </fieldset>
          <div
            class="mt-3"
            v-for="error of v$.snellenRightEye.$errors"
            :key="error.$uid"
          >
            <div class="text-red-700">{{ error.$message }}</div>
          </div>
        </div>

        <div class="sm:col-span-3">
          <label class="text-base font-medium text-gray-900"
            >Acuity without glasses / contact lenses</label
          >
          <p class="text-sm leading-5 text-gray-500">
            Indicate the level of acuity below?
          </p>
          <fieldset class="mt-4">
            <legend class="sr-only">Notification method</legend>
            <div class="space-y-4">
              <div
                v-for="notificationMethod in snellenRightWithOut"
                :key="notificationMethod.id"
                class="flex items-center"
              >
                <input
                  :value="notificationMethod.title"
                  v-model="formData.snellenRightEyeWithoutGlasses"
                  :id="notificationMethod.id"
                  :name="notificationMethod.id"
                  type="radio"
                  class="w-4 h-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"
                />
                <label
                  :for="notificationMethod.id"
                  class="block ml-3 text-sm font-medium text-gray-700"
                  >{{ notificationMethod.title }}</label
                >
              </div>
            </div>
          </fieldset>
          <div
            class="mt-3"
            v-for="error of v$.snellenRightEyeWithoutGlasses.$errors"
            :key="error.$uid"
          >
            <div class="text-red-700">{{ error.$message }}</div>
          </div>
        </div>

        <div class="sm:col-span-3">
          <label
            for="identification"
            class="block text-sm font-medium text-stone-400"
          >
            Actual Horizontal temporal field*</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              v-model="formData.temporalRight"
              @blur="v$.temporalRight.$touch"
              name="temporal-right"
              id="temporail-right"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <div
              class=""
              v-for="error of v$.temporalRight.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-3">
          <label
            for="identification"
            class="block text-sm font-medium text-stone-400"
          >
            Actual Horizontal total field*</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              v-model="formData.totalRight"
              @blur="v$.totalRight.$touch"
              name="total-right"
              id="total-right"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <div
              class=""
              v-for="error of v$.totalRight.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <!-- ################################################################# -->
        <!-- LEFT -->
        <!-- ################################################################## -->

        <div class="sm:col-span-6">
          <h3
            class="p-2 text-lg font-semibold leading-6 text-gray-900 bg-stone-100"
          >
            Left Eye
          </h3>
        </div>

        <div class="sm:col-span-3">
          <label class="text-base font-medium text-gray-900"
            >Acuity with glasses / contact lenses</label
          >
          <p class="text-sm leading-5 text-gray-500">
            Indicate the level of acuity below?
          </p>
          <fieldset class="mt-4">
            <legend class="sr-only">Notification method</legend>
            <div class="space-y-4">
              <div
                v-for="notificationMethod in snellenLeft"
                :key="notificationMethod.id"
                class="flex items-center"
              >
                <input
                  :value="notificationMethod.title"
                  v-model="formData.snellenLeftEye"
                  :id="notificationMethod.id"
                  :name="notificationMethod.id"
                  type="radio"
                  class="w-4 h-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"
                />
                <label
                  :for="notificationMethod.id"
                  class="block ml-3 text-sm font-medium text-gray-700"
                  >{{ notificationMethod.title }}</label
                >
              </div>
            </div>
          </fieldset>
          <div
            class="mt-3"
            v-for="error of v$.snellenLeftEye.$errors"
            :key="error.$uid"
          >
            <div class="text-red-700">{{ error.$message }}</div>
          </div>
        </div>

        <div class="sm:col-span-3">
          <label class="text-base font-medium text-gray-900"
            >Acuity without glasses / contact lenses</label
          >
          <p class="text-sm leading-5 text-gray-500">
            Indicate the level of acuity below?
          </p>
          <fieldset class="mt-4">
            <legend class="sr-only">Notification method</legend>
            <div class="space-y-4">
              <div
                v-for="notificationMethod in snellenLeftWithOut"
                :key="notificationMethod.id"
                class="flex items-center"
              >
                <input
                  :value="notificationMethod.title"
                  v-model="formData.snellenLeftEyeWithoutGlasses"
                  :id="notificationMethod.id"
                  :name="notificationMethod.id"
                  type="radio"
                  class="w-4 h-4 text-indigo-600 border-gray-300 focus:ring-indigo-500"
                />
                <label
                  :for="notificationMethod.id"
                  class="block ml-3 text-sm font-medium text-gray-700"
                  >{{ notificationMethod.title }}</label
                >
              </div>
            </div>
          </fieldset>
          <div
            class=""
            v-for="error of v$.snellenLeftEyeWithoutGlasses.$errors"
            :key="error.$uid"
          >
            <div class="text-red-700">{{ error.$message }}</div>
          </div>
        </div>

        <div class="sm:col-span-3">
          <label
            for="temporal-left"
            class="block text-sm font-medium text-stone-400"
          >
            Actual Horizontal temporal field*</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              v-model="formData.temporalLeft"
              @blur="v$.temporalLeft.$touch"
              name="temporalLeft"
              id="temporalLeft"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <div
              class=""
              v-for="error of v$.temporalLeft.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-3">
          <label
            for="actual-left"
            class="block text-sm font-medium text-stone-400"
          >
            Actual Horizontal total field*</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              v-model="formData.totalLeft"
              @blur="v$.totalLeft.$touch"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <div
              class=""
              v-for="error of v$.totalLeft.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-6">
          <h3
            class="p-2 text-lg font-semibold leading-6 text-gray-900 bg-stone-100"
          >
            Both Eyes
          </h3>
        </div>

        <div class="sm:col-span-4">
          <div class="flex items-center justify-between">
            <h2 class="text-sm font-medium text-stone-400">
              Color Discrimination*
            </h2>
          </div>

          <RadioGroup
            v-model="formData.colorDiscrimination"
            class="mt-2"
          >
            <RadioGroupLabel class="sr-only">
              Choose an option
            </RadioGroupLabel>
            <div class="grid grid-cols-3 gap-3 sm:grid-cols-6">
              <RadioGroupOption
                as="template"
                v-for="option in memoryOptions"
                :key="option.name"
                :value="option.name"
                v-slot="{ active, checked }"
              >
                <div
                  :class="[
                    active
                      ? 'ring-2 ring-offset-2 ring-teal-500'
                      : '',
                    checked
                      ? 'bg-teal-600 border-transparent text-white hover:bg-teal-700'
                      : 'bg-white border-gray-200 text-gray-900 hover:bg-gray-50',
                    'cursor-pointer focus:outline-none border rounded-md py-3 px-3 flex items-center justify-center text-sm font-medium sm:flex-1',
                  ]"
                >
                  <RadioGroupLabel as="p">
                    {{ option.name }}
                  </RadioGroupLabel>
                </div>
              </RadioGroupOption>
            </div>
          </RadioGroup>
          <div
            class="mt-1"
            v-for="error of v$.colorDiscrimination.$errors"
            :key="error.$uid"
          >
            <div class="text-red-700">{{ error.$message }}</div>
          </div>
        </div>

        <div class="sm:col-span-4">
          <div class="mt-1">
            <Listbox as="div" v-model="selectedClinic">
              <ListboxLabel
                class="block text-sm font-medium text-stone-400"
              >
                Clinic Attended
              </ListboxLabel>
              <div class="relative mt-1">
                <ListboxButton
                  class="relative w-full py-2 pl-3 pr-10 text-left bg-white border border-gray-300 rounded-sm shadow-sm cursor-default focus:outline-none focus:ring-1 focus:ring-stone-500 focus:border-stone-300 sm:text-sm"
                >
                  <span class="inline-flex w-full truncate">
                    <span class="truncate">{{
                      selectedClinic.clinicName
                    }}</span></span
                  >
                  <span
                    class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"
                  >
                    <ChevronUpDownIcon
                      class="w-5 h-5 text-gray-400"
                      aria-hidden="true"
                    />
                  </span>
                </ListboxButton>

                <transition
                  leave-active-class="transition duration-100 ease-in"
                  leave-from-class="opacity-100"
                  leave-to-class="opacity-0"
                >
                  <ListboxOptions
                    class="absolute z-10 w-full py-1 mt-1 overflow-auto text-base bg-white rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
                  >
                    <ListboxOption
                      as="template"
                      v-for="clinic in clinics"
                      :key="clinic.id"
                      :value="clinic"
                      v-slot="{ active, selected }"
                    >
                      <li
                        :class="[
                          active
                            ? 'text-white bg-stone-600'
                            : 'text-gray-900',
                          'cursor-default select-none relative py-2 pl-3 pr-9',
                        ]"
                      >
                        <div class="flex">
                          <span
                            :class="[
                              selected
                                ? 'font-semibold'
                                : 'font-normal',
                              'truncate',
                            ]"
                          >
                            {{ clinic.clinicName }}
                          </span>
                          <span
                            :class="[
                              active
                                ? 'text-stone-200'
                                : 'text-gray-500',
                              'ml-2 truncate',
                            ]"
                          >
                          </span>
                        </div>

                        <span
                          v-if="selected"
                          :class="[
                            active ? 'text-white' : 'text-stone-600',
                            'absolute inset-y-0 right-0 flex items-center pr-4',
                          ]"
                        >
                          <CheckIcon
                            class="w-5 h-5"
                            aria-hidden="true"
                          />
                        </span>
                      </li>
                    </ListboxOption>
                  </ListboxOptions>
                </transition>
              </div>
            </Listbox>
            <div
              class=""
              v-for="error of v$.clinicId.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-3">
          <v-date-picker
            color="teal"
            v-model="formData.dateOfScreening"
            :masks="masks"
            class=""
          >
            <template v-slot="{ inputValue, togglePopover }">
              <label
                for="employment_start_date"
                class="block mb-1 text-sm font-medium text-stone-400"
                >Date of Screening*</label
              >
              <input
                class="w-full px-2 py-2 font-semibold border rounded-sm shadow-sm border-stone-300 focus:border-stone-500 focus:outline-none focus:ring-1 focus:ring-stone-500"
                :value="inputValue"
                @click="togglePopover"
              />
            </template>
          </v-date-picker>
        </div>

        <div class="sm:col-span-3">
          <label
            for="actual-left"
            class="block text-sm font-medium text-stone-400"
          >
            Performed By</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              v-model="formData.performedBy"
              name="performedby"
              id="performedby"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="description"
            class="block text-sm font-medium text-stone-400"
            >Comments</label
          >
          <div class="mt-1">
            <textarea
              autocomplete="off"
              row="4"
              type="text"
              name="about"
              id="about"
              v-model="formData.comment"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800 placeholder:text-slate-300 placeholder:font-normal"
              placeholder=""
            ></textarea>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import {
  ArrowSmallLeftIcon,
  CheckIcon,
  ChevronUpDownIcon,
} from "@heroicons/vue/24/solid";
import gql from "graphql-tag";
import { useQuery, useMutation } from "@vue/apollo-composable";
import { useRoute, useRouter } from "vue-router";
import { reactive, ref, watch, computed } from "vue";
import useVuelidate from "@vuelidate/core";
import { required, helpers } from "@vuelidate/validators";
import { date } from "quasar";

const route = useRoute();
const router = useRouter();

const PATIENT = gql`
  query getPatient($id: ID!) {
    patient(id: $id) {
      id
      fullName
      identificationNumber
    }
  }
`;

const CLINICS = gql`
  query listClinics($id: ID!) {
    clinics(organisationId: $id) {
      id
      clinicName
    }
  }
`;

const { result: clinicResult } = useQuery(
  CLINICS,
  { id: 1 },
  { fetchPolicy: "cache-and-network" },
);

const todayDate = new Date();

const selectedClinic = ref("");

watch(
  () => selectedClinic.value,
  (outcome) => {
    formData.clinicId = parseInt(outcome.id);
  },
);

const clinics = computed(() => clinicResult?.value.clinics ?? []);

const masks = {
  input: "YYYY/MM/DD",
  mode: "date",
  data: ["L", "YYYY-MM-DD", "YYYY/MM/DD"],
};

const { result: patientResult } = useQuery(
  PATIENT,
  { id: route.params.patient_id },
  { fetchPolicy: "cache-and-network" },
);

const patient = computed(() => {
  return patientResult.value?.patient ?? "Patient";
});

const CREATE_VISION = gql`
  mutation createVision($input: VisionScreeningInput!) {
    createVision(input: $input) {
      vision {
        id
        reportName
      }
      success
      errors {
        path
        message
      }
    }
  }
`;

const {
  mutate: sendForm,
  onDone,
  loading: sendFormLoading,
} = useMutation(CREATE_VISION, () => ({
  variables: {
    input: {
      ...formData,
    },
  },
}));

const rules = {
  name: {
    required: helpers.withMessage(
      "Document title is required.",
      required,
    ),
  },
  totalLeft: {
    required: helpers.withMessage(
      "Act. Horiz. Total field can't be empty.",
      required,
    ),
  },
  temporalLeft: {
    required: helpers.withMessage(
      "Act. Horiz. Temporal field can't be empty.",
      required,
    ),
  },
  snellenLeftEye: {
    required: helpers.withMessage(
      "Snellen Left Eye with Glasses field can't be empty.",
      required,
    ),
  },
  snellenLeftEyeWithoutGlasses: {
    required: helpers.withMessage(
      "Snellen Left Eye without glasses field can't be empty.",
      required,
    ),
  },

  totalRight: {
    required: helpers.withMessage(
      "Act. Horiz. Total field can't be empty.",
      required,
    ),
  },
  temporalRight: {
    required: helpers.withMessage(
      "Act. Horiz. Temporal field can't be empty.",
      required,
    ),
  },
  snellenRightEye: {
    required: helpers.withMessage(
      "Snellen Right Eye with Glasses field can't be empty.",
      required,
    ),
  },
  snellenRightEyeWithoutGlasses: {
    required: helpers.withMessage(
      "Snellen Right Eye without glasses field can't be empty.",
      required,
    ),
  },

  colorDiscrimination: {
    required: helpers.withMessage(
      "Color Discrimination field can't be empty.",
      required,
    ),
  },

  dateOfScreening: {
    required: helpers.withMessage(
      "Date field can't be empty.",
      required,
    ),
  },

  clinicId: {
    required: helpers.withMessage(
      "Clinic must be specified.",
      required,
    ),
  },
};

const visionName = computed(() => {
  const name = patient?.value.fullName;
  const currdate = date.formatDate(Date.now(), "YYYYMMDD");

  return `[Vision Screening] ${currdate} ${name}`;
});

const formData = reactive({
  colorDiscrimination: "Able",
  comment: "",
  dateOfScreening: todayDate,
  clinicId: "",
  patientId: parseInt(route.params.patient_id),

  totalLeft: "",
  totalRight: "",

  temporalLeft: "",
  temporalRight: "",

  snellenRightEye: "",
  snellenRightEyeWithoutGlasses: "",

  snellenLeftEye: "",
  snellenLeftEyeWithoutGlasses: "",

  name: visionName?.value,
  performedBy: "",
});

watch(
  () => patient.value,
  (outcome) => {
    formData.patientId = parseInt(outcome.id);
    formData.name = visionName.value;
  },
);

const v$ = useVuelidate(rules, formData);

const submitForm = async () => {
  const isFormCorrect = await v$.value.$validate();
  if (!isFormCorrect) return;
  console.log(formData);
  sendForm();
};
const snellenRight = [
  { id: "n/a-r", title: "Not Applicable" },
  { id: "0.1-r", title: "6/60" },
  { id: "0.2-r", title: "6/36" },
  { id: "0.3-r", title: "6/24" },
  { id: "0.4-r", title: "6/18" },
  { id: "0.5-r", title: "6/12" },
  { id: "0.6-r", title: "6/9" },
  { id: "0.7-r", title: "6/9+" },
  { id: "0.8-r", title: "6/7.5" },
  { id: "0.9-r", title: "6/7.5+" },
  { id: "1.0-r", title: "6/6" },
  { id: "1.1-r", title: "6/6+" },
  { id: "1.2-r", title: "6/5+" },
];
const snellenRightWithOut = [
  { id: "n/a-wo", title: "Not Applicable" },
  { id: "0.1-wo", title: "6/60" },
  { id: "0.2-wo", title: "6/36" },
  { id: "0.3-wo", title: "6/24" },
  { id: "0.4-wo", title: "6/18" },
  { id: "0.5-wo", title: "6/12" },
  { id: "0.6-wo", title: "6/9" },
  { id: "0.7-wo", title: "6/9+" },
  { id: "0.8-wo", title: "6/7.5" },
  { id: "0.9-wo", title: "6/7.5+" },
  { id: "1.0-wo", title: "6/6" },
  { id: "1.1-wo", title: "6/6+" },
  { id: "1.2-wo", title: "6/5+" },
];

const snellenLeft = [
  { id: "n/a", title: "Not Applicable" },
  { id: "0.1", title: "6/60" },
  { id: "0.2", title: "6/36" },
  { id: "0.3", title: "6/24" },
  { id: "0.4", title: "6/18" },
  { id: "0.5", title: "6/12" },
  { id: "0.6", title: "6/9" },
  { id: "0.7", title: "6/9+" },
  { id: "0.8", title: "6/7.5" },
  { id: "0.9", title: "6/6+" },
  { id: "1.0", title: "6/6" },
  { id: "1.1", title: "6/6+" },
  { id: "1.2", title: "6/5+" },
];
const snellenLeftWithOut = [
  { id: "n/a-lwo", title: "Not Applicable" },
  { id: "0.1-lwo", title: "6/60" },
  { id: "0.2-lwo", title: "6/36" },
  { id: "0.3-lwo", title: "6/24" },
  { id: "0.4-lwo", title: "6/18" },
  { id: "0.5-lwo", title: "6/12" },
  { id: "0.6-lwo", title: "6/9" },
  { id: "0.7-lwo", title: "6/9+" },
  { id: "0.8-lwo", title: "6/7.5" },
  { id: "0.9-lwo", title: "6/7.5+" },
  { id: "1.0-lwo", title: "6/6" },
  { id: "1.1-lwo", title: "6/6+" },
  { id: "1.2-lwo", title: "6/5+" },
];

onDone((result) => {
  if (result.data.createVision.success == true) {
    router.push({
      name: "vision_screenings",
      params: { id: route.params.patient_id },
    });
  } else
    console.log("From Error:", result.data?.createVision.success);
});

const memoryOptions = [{ name: "Able" }, { name: "Unable" }];
</script>
