<template>
  <q-page padding>
    <PageHeading class="max-w-4xl">
      <template #navigation>
        <div class="flex items-center text-gray-500 text-uppercase">
          <ArrowSmallLeftIcon class="w-5 h-5" />
          <router-link to="/medicals">Back</router-link>
        </div>
      </template>
      <template #heading>
        <div class="capitalize">Medical: {{ medical.name }}</div>
      </template>

      <template #buttons>
        <q-btn
          flat
          class="mr-4 text-white bg-teal-700 text-bold"
          padding="sm"
          no-caps
          :loading="sendFormLoading"
          @click="setIsOpen(true)"
          type="submit"
        >
          <template v-slot:loading>
            <q-spinner-ios />
          </template>
          <div class="text-sm">Generate Report</div>
        </q-btn>
      </template>
    </PageHeading>

    <div class="max-w-4xl">
      <div
        class="flex flex-col p-4 rounded-sm shadow md:mt-8 bg-stone-50"
      >
        <div class="flex justify-between">
          <div class="mb-6 font-bold text-stone-700">Details</div>

          <span class="justify-end rounded-md">
            <router-link
              class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs leading-4 font-medium rounded text-stone-100 bg-stone-800 hover:text-stone-100 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:text-gray-800 active:bg-gray-50 transition ease-in-out duration-150"
              :to="{ name: 'edit_medical', id: route.params.id }"
            >
              Edit Medical
            </router-link>
          </span>
        </div>

        <div class="max-w-4xl">
          <div class="grid grid-cols-2 gap-4">
            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-400"
              >
                Patient
              </div>
              <div class="font-bold">
                {{ medical.patient?.fullName }}
              </div>
            </div>
            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-400"
              >
                Type of Medical
              </div>
              <div class="font-bold">
                {{ medical.medicalType }}
              </div>
            </div>
            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-400"
              >
                Outcome
              </div>
              <div class="font-bold">
                {{ medical.outcome }}
              </div>
            </div>

            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-400"
              >
                Validity Period
              </div>
              <div class="font-bold">
                {{ medical.medicalExaminationDate }} ->
                {{ medical.medicalExpiryDate }}
              </div>
            </div>

            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-400"
              >
                Clinic Used
              </div>
              <div class="font-bold">
                {{ medical.clinic?.clinicName }}
              </div>
            </div>

            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-400"
              >
                Status
              </div>
              <div class="font-bold capitalize">
                {{ medical.status }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="max-w-4xl">
        <div
          class="flex flex-col p-4 mt-8 rounded-sm shadow bg-stone-50"
        >
          <div class="sm:col-span-4">
            <h3 class="text-lg font-semibold leading-6 text-gray-900">
              Exclusions
            </h3>
            <p class="text-sm leading-5 text-gray-500">
              {{
                medical.exclusionComment
                  ? medical.exclusionComment
                  : "No exclusions noted"
              }}
            </p>
          </div>
        </div>
      </div>

      <div class="max-w-4xl">
        <div
          class="flex flex-col p-4 mt-8 rounded-sm shadow bg-stone-50"
        >
          <div class="sm:col-span-4">
            <h3 class="text-lg font-semibold leading-6 text-gray-900">
              Referrals
            </h3>
            <p class="text-sm leading-5 text-gray-500">
              {{
                medical.referralComment
                  ? medical.referralComment
                  : "No refferals required"
              }}
            </p>
          </div>
        </div>
      </div>

      <div class="max-w-4xl">
        <div
          class="flex flex-col p-4 mt-8 rounded-sm shadow bg-stone-50"
        >
          <div class="sm:col-span-4">
            <h3 class="text-lg font-semibold leading-6 text-gray-900">
              Final Comments
            </h3>
            <p class="text-sm leading-5 text-gray-500">
              {{
                medical.outcomeComment
                  ? medical.outcomeComment
                  : "No notable comments"
              }}
            </p>
          </div>
        </div>
      </div>

      <div class="max-w-sm">
        <div class="mt-10">
          <div class="" v-if="testsPerformedCount > 0">
            <div class="sm:flex sm:items-center">
              <div class="sm:flex-auto">
                <h1 class="text-xl font-semibold text-gray-900">
                  Tests Performed ({{ testsPerformedCount }})
                </h1>
                <p class="mt-2 text-sm text-gray-700">
                  A table of tests performed for this medical
                </p>
              </div>
            </div>
            <div class="flex flex-col mt-8">
              <div
                class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"
              >
                <div
                  class="inline-block min-w-full px-6 py-2 align-middle lg:px-8"
                >
                  <div
                    class="overflow-hidden rounded-sm shadow ring-1 ring-black ring-opacity-5"
                  >
                    <table
                      class="min-w-full divide-y divide-gray-300"
                    >
                      <thead class="bg-stone-50">
                        <tr>
                          <th
                            scope="col"
                            class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-4"
                          >
                            Test Name
                          </th>
                        </tr>
                      </thead>
                      <tbody
                        class="bg-white divide-y divide-gray-200"
                      >
                        <tr
                          v-for="test in testsPerformed"
                          :key="test"
                        >
                          <td
                            class="py-2 pl-6 text-sm text-gray-500 whitespace-nowrap"
                          >
                            {{ test }}
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="" v-else>
            <div class="sm:flex sm:items-center">
              <div class="sm:flex-auto">
                <h1 class="text-xl font-semibold text-gray-900">
                  Tests Performed ({{ testsPerformedCount }})
                </h1>
                <p class="mt-2 text-sm text-gray-700">
                  A table of tests performed for this medical
                </p>
              </div>
            </div>
            <div class="flex flex-col mt-8">
              <div
                class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"
              >
                <div
                  class="inline-block min-w-full px-6 py-2 align-middle lg:px-8"
                >
                  <div
                    class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-sm"
                  >
                    <table
                      class="min-w-full divide-y divide-gray-300"
                    >
                      <thead class="bg-stone-50">
                        <tr>
                          <th
                            scope="col"
                            class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"
                          ></th>
                        </tr>
                      </thead>
                      <tbody
                        class="flex items-center justify-center p-4 bg-white divide-y divide-gray-200"
                      >
                        <tr class="">
                          <EmptyState
                            class="flex justify-center p-4"
                          />
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="max-w-md">
        <div class="mt-10">
          <div class="" v-if="reportCount > 0">
            <div class="sm:flex sm:items-center">
              <div class="sm:flex-auto">
                <h1
                  class="flex items-center text-xl font-semibold text-gray-900"
                >
                  Reports ({{ reportCount }})
                  <label
                    class="flex ml-3 text-sm font-medium text-gray-700"
                  >
                    <span class="ml-1">
                      <ArrowPathIcon
                        as="span"
                        class="w-4 h-4 text-stone-500"
                        v-if="!loading"
                        @click="refetch()"
                      />
                    </span>
                    <q-tooltip v-if="!loading" :offset="[0, 8]"
                      >Refetch Reports</q-tooltip
                    >

                    <q-spinner-ios
                      v-if="loading"
                      color="brown"
                      size="1.2em"
                    />
                    <q-tooltip v-if="loading" :offset="[0, 8]"
                      >Loading</q-tooltip
                    >
                  </label>
                </h1>
                <p class="mt-2 text-sm text-gray-700">
                  A list of generated reports for this medical
                </p>
              </div>
            </div>
            <div class="flex flex-col mt-8">
              <div
                class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"
              >
                <div
                  class="inline-block min-w-full px-6 py-2 align-middle lg:px-8"
                >
                  <div
                    class="overflow-hidden rounded-sm shadow ring-1 ring-black ring-opacity-5"
                  >
                    <table
                      class="min-w-full divide-y divide-gray-300"
                    >
                      <thead class="bg-stone-50">
                        <tr>
                          <th
                            scope="col"
                            class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-4"
                          >
                            File Attachment
                          </th>
                        </tr>
                      </thead>
                      <tbody
                        class="bg-white divide-y divide-gray-200"
                      >
                        <tr
                          v-for="report in reports"
                          :key="report.id"
                        >
                          <td
                            class="flex justify-between py-2 pl-6 text-sm text-gray-500 whitespace-nowrap"
                          >
                            <span>
                              {{ report.fileName }}
                            </span>
                            <span class="mr-4">
                              <a :href="report.url" target="_blank">
                                <ArrowDownTrayIcon
                                  class="w-5 h-5 text-teal-900 hover:text-teal-700"
                                />
                              </a>
                            </span>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="" v-else>
            <div class="sm:flex sm:items-center">
              <div class="sm:flex-auto">
                <h1
                  class="flex items-center text-xl font-semibold text-gray-900"
                >
                  Reports
                  <label
                    class="flex ml-3 text-sm font-medium text-gray-700"
                  >
                    <span class="ml-1">
                      <ArrowPathIcon
                        as="span"
                        class="w-4 h-4 text-stone-500"
                        v-if="!loading"
                        @click="refetch()"
                      />
                    </span>
                    <q-tooltip v-if="!loading" :offset="[0, 8]"
                      >Refetch Reports</q-tooltip
                    >

                    <q-spinner-ios
                      v-if="loading"
                      color="brown"
                      size="1.2em"
                    />
                    <q-tooltip v-if="loading" :offset="[0, 8]"
                      >Loading</q-tooltip
                    >
                  </label>
                </h1>
                <p class="mt-2 text-sm text-gray-700">
                  A list of generated reports for this medical
                </p>
              </div>
            </div>
            <div class="flex flex-col mt-8">
              <div
                class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"
              >
                <div
                  class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8"
                >
                  <div
                    class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg"
                  >
                    <table
                      class="min-w-full divide-y divide-gray-300"
                    >
                      <thead class="bg-stone-50">
                        <tr>
                          <th
                            scope="col"
                            class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"
                          ></th>
                        </tr>
                      </thead>
                      <tbody
                        class="flex items-center justify-center p-4 bg-white divide-y divide-gray-200"
                      >
                        <tr class="">
                          <EmptyState
                            class="flex justify-center p-4"
                          />
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </q-page>

  <TransitionRoot appear :show="isOpen" as="template">
    <Dialog as="div" @close="setIsOpen" class="relative z-10">
      <TransitionChild
        as="template"
        enter="duration-300 ease-out"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="duration-200 ease-in"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-opacity-25 bg-black/30" />
      </TransitionChild>

      <div class="fixed inset-0 overflow-y-auto">
        <div
          class="flex items-center justify-center min-h-full p-4 text-center"
        >
          <TransitionChild
            as="template"
            enter="duration-300 ease-out"
            enter-from="opacity-0 scale-95"
            enter-to="opacity-100 scale-100"
            leave="duration-200 ease-in"
            leave-from="opacity-100 scale-100"
            leave-to="opacity-0 scale-95"
          >
            <DialogPanel
              class="w-full max-w-md p-6 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-2xl"
            >
              <DialogTitle
                as="h3"
                class="text-lg font-medium leading-6 text-gray-900"
              >
                Generate Report
              </DialogTitle>
              <div class="mt-2">
                <p class="mb-4 text-sm text-gray-500">
                  Enter your security pin to generate this report.
                </p>

                <div class="mt-1 mb-4">
                  <input
                    autocomplete="off"
                    type="password"
                    name="pin"
                    id="pin"
                    class="block font-semibold rounded-sm shadow-sm w-42 focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
                    v-model="formData.pin"
                    @blur="v$.pin.$touch"
                    placeholder=""
                  />
                  <div
                    class=""
                    v-for="error of v$.pin.$errors"
                    :key="error.$uid"
                  >
                    <div class="text-red-700">
                      {{ error.$message }}
                    </div>
                  </div>
                </div>
              </div>

              <div class="mt-4">
                <button
                  type="button"
                  class="inline-flex justify-center px-4 py-2 text-sm font-medium border border-transparent rounded-md text-stone-900 bg-stone-100 hover:bg-stone-300 focus:outline-none focus-visible:ring-2 focus-visible:ring-teal-500 focus-visible:ring-offset-2"
                  @click="submitForm"
                >
                  Generate
                </button>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup>
import { useRoute, useRouter } from "vue-router";
import { useQuery, useMutation } from "@vue/apollo-composable";
import gql from "graphql-tag";
import { computed, ref, reactive } from "vue";
import {
  ArrowSmallLeftIcon,
  ArrowDownTrayIcon,
  ArrowPathIcon,
} from "@heroicons/vue/24/outline";
import { Dialog, DialogTitle } from "@headlessui/vue";
import useVuelidate from "@vuelidate/core";
import { required, helpers, sameAs } from "@vuelidate/validators";
import { useDebounceFn } from "@vueuse/core";

const pin = ref("4706");

const rules = {
  pin: {
    required: helpers.withMessage("Pin is required.", required),
    sameAsRef: helpers.withMessage("Pin is invalid.", sameAs(pin)),
  },
};

const formData = reactive({
  pin: "",
});

const v$ = useVuelidate(rules, formData);

const GENERATE_REPORT = gql`
  mutation generateMed($id: ID!) {
    createMedicalReport(medicalId: $id) {
      success
      errors {
        path
        message
      }
      medical {
        id
        status
      }
    }
  }
`;

const {
  mutate: sendForm,
  onDone,
  loading: sendFormLoading,
} = useMutation(GENERATE_REPORT, () => ({
  variables: {
    id: route.params.id,
  },
}));

const submitForm = async () => {
  const isFormCorrect = await v$.value.$validate();
  if (!isFormCorrect) return;
  sendForm();
  setIsOpen(false);
  v$.value?.$reset();
  formData.pin = "";
};

const debouncedFetch = useDebounceFn(
  () => {
    refetch();
  },
  3000,
  { maxWait: 6000 },
);

onDone((result) => {
  if (result.data.createMedicalReport.success == true) {
    debouncedFetch();
  } else {
    console.log(
      "From Error:",
      result.data?.createMedicalReport.success,
    );
  }
});

const route = useRoute();

const isOpen = ref(false);

function setIsOpen(value) {
  isOpen.value = value;
}
const GET_MEDICAL = gql`
  query getMedical($id: ID!) {
    medical(id: $id) {
      patient {
        fullName
        id
      }
      id
      name
      medicalType
      medicalExpiryDate
      medicalExaminationDate
      referralComment
      status
      outcomeComment
      exclusionComment
      outcome
      audioPerformed
      cannabisPerformed
      ecgPerformed
      heatPerformed
      heightPerformed
      physicalExamPerformed
      spiroPerformed
      visualPerformed
      xrayPerformed
      clinic {
        id
        clinicName
      }
      assessmentReports {
        url
        fileName
        id
      }
    }
  }
`;
const { result, refetch, loading } = useQuery(
  GET_MEDICAL,
  { id: route.params.id },
  { fetchPolicy: "cache-and-network" },
);

const medical = computed(() => {
  return result.value?.medical ?? "No Medical";
});

const testsPerformed = computed(() => {
  var testsPerformed = [];

  if (medical.value.audioPerformed) testsPerformed.push("Audiometry");
  if (medical.value.spiroPerformed) testsPerformed.push("Spirometry");
  if (medical.value.visualPerformed)
    testsPerformed.push("Vision Screening");
  if (medical.value.cannabisPerformed)
    testsPerformed.push("Drug Screening");
  if (medical.value.physicalExamPerformed)
    testsPerformed.push("Physical Exam");
  if (medical.value.heightPerformed)
    testsPerformed.push("Work at Heights");
  if (medical.value.heatPerformed) testsPerformed.push("Heat");
  if (medical.value.ecgPerformed) testsPerformed.push("ECG");
  if (medical.value.xrayPerformed)
    testsPerformed.push("Xray Screening");

  return testsPerformed;
});

const testsPerformedCount = computed(() => {
  return testsPerformed.value.length ?? 0;
});

const reports = computed(() => {
  return medical.value?.assessmentReports;
});

const reportCount = computed(() => {
  return reports.value?.length ?? 0;
});
</script>
