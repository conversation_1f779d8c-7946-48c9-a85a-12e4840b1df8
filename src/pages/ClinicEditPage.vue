<template>
  <q-page padding>
    <form @submit.prevent="submitForm">
      <PageHeading>
        <template #navigation>
          <div class="flex items-center text-gray-500 text-uppercase">
            <router-link class="flex" to="/settings">
              <ArrowSmallLeftIcon class="w-5 h-5" />
              Back
            </router-link>
          </div>
        </template>
        <template #heading>
          <div class="capitalize">{{ clinic.clinicName }}</div>
        </template>

        <template #buttons>
          <q-btn
            flat
            class="text-white bg-teal-700 text-bold"
            padding="md"
            no-caps
            :loading="sendFormLoading"
            type="submit"
          >
            <template v-slot:loading>
              <q-spinner-ios />
            </template>
            <CheckIcon class="w-5 h-5 mr-1 font-bold" />
            <div class="text-sm">Save</div>
          </q-btn>
        </template>
      </PageHeading>
    </form>

    <div class="max-w-2xl">
      <div
        class="grid grid-cols-1 mt-6 gap-x-4 gap-y-6 sm:grid-cols-6"
      >
        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Clinic Information
          </h3>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Clinic Name*</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              v-model="formData.clinicName"
              @blur="v$.clinicName.$touch"
              name="clinicName"
              id="clinicName"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <div
              class=""
              v-for="error of v$.clinicName.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-3">
          <label
            for=""
            class="block text-sm font-medium text-stone-400"
            >Phone Number*</label
          >
          <div class="mt-1">
            <input
              type="text"
              name="phoneNumber"
              id="phoneNumber"
              autocomplete="off"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              v-model="formData.phoneNumber"
              placeholder=""
            />
          </div>
        </div>

        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Location
          </h3>
        </div>

        <div class="sm:col-span-5">
          <label
            for="street_address"
            class="block text-sm font-medium text-stone-400"
            >Street Address (Line 1)*</label
          >
          <div class="mt-1">
            <input
              type="text"
              name="phyiscalAddress"
              id="phyiscalAddress"
              v-model="formData.physicalAddress"
              autocomplete="new-street-address"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
          </div>
        </div>

        <div class="sm:col-span-5">
          <label
            for="phyiscalAddress2"
            class="block text-sm font-medium text-stone-400"
            >Street Address (Line 2)</label
          >
          <div class="mt-1">
            <input
              type="text"
              name="phyiscalAddress2"
              id="phyiscalAddress2"
              v-model="formData.physicalAddress2"
              autocomplete="new-street-address2"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import {
  ArrowSmallLeftIcon,
  CheckIcon,
} from "@heroicons/vue/24/solid";
import gql from "graphql-tag";
import { useQuery, useMutation } from "@vue/apollo-composable";
import { useRoute, useRouter } from "vue-router";
import { watch, reactive, ref, computed } from "vue";
import useVuelidate from "@vuelidate/core";
import { required, helpers } from "@vuelidate/validators";

const route = useRoute();

const GET_CLINIC = gql`
  query getClinic($id: ID!) {
    clinic(id: $id) {
      id
      clinicName
      physicalAddress
      physicalAddress2
      phoneNumber
    }
  }
`;

const UPDATE_CLINIC = gql`
  mutation updateClinic($id: ID!, $input: ClinicInput!) {
    updateClinic(id: $id, input: $input) {
      clinic {
        clinicName
        physicalAddress
        physicalAddress2
        phoneNumber
      }
      success
      errors {
        path
        message
      }
    }
  }
`;

const { result } = useQuery(
  GET_CLINIC,
  {
    id: route.params.id,
  },
  {
    fetchPolicy: "cache-and-network",
  },
);

const clinic = computed(() => {
  return result.value?.clinic ?? "Clinic";
});

const {
  mutate: sendForm,
  onDone,
  onError,
  loading: sendFormLoading,
} = useMutation(UPDATE_CLINIC, () => ({
  variables: {
    id: route.params.id,
    input: {
      ...formData,
    },
  },
}));

const rules = {
  clinicName: {
    required: helpers.withMessage(
      "Clinic name is required.",
      required,
    ),
  },
  physicalAddress: {
    required: helpers.withMessage(
      "Clinic address is required.",
      required,
    ),
  },
};

const formData = reactive({
  clinicName: "",
  physicalAddress: "",
  physicalAddress2: "",
  phoneNumber: "",
});

watch(
  () => clinic.value,
  (outcome) => {
    formData.clinicName = outcome.clinicName;
    formData.physicalAddress = outcome.physicalAddress;
    formData.physicalAddress2 = outcome.physicalAddress2;
    formData.phoneNumber = outcome.phoneNumber;
  },
);

const v$ = useVuelidate(rules, formData);

const submitForm = async () => {
  const isFormCorrect = await v$.value.$validate();
  if (!isFormCorrect) return;
  sendForm();
};

const router = useRouter();

onDone((result) => {
  if (result.data.updateClinic.success == true) {
    router.push(`/settings`);
  } else {
    console.log("From Error:", result.data?.updateClinic.success);
  }
});
</script>
