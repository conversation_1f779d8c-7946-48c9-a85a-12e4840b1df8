<template>
  <q-layout>
    <q-page-container>
      <q-page class="flex flex-col justify-center h-full">
        <div
          class="flex items-center justify-center min-h-full px-4 py-12 sm:px-6 lg:px-8"
        >
          <div class="w-full max-w-sm space-y-8">
            <div>
              <img
                class="w-auto h-12 mx-auto"
                src="https://tailwindui.com/img/logos/mark.svg?color=stone&shade=600"
                alt="Occusolve"
              />
              <h2
                class="mt-6 text-3xl font-bold tracking-tight text-center text-gray-900"
              >
                Sign in to your account
              </h2>
            </div>

            <form @submit.prevent="submitForm" class="mt-8 space-y-6">
              <div class="-space-y-px rounded-md shadow-sm">
                <div>
                  <label for="email-address" class="sr-only"
                    >Email address</label
                  >
                  <input
                    id="email-address"
                    name="email"
                    type="email"
                    autocomplete="off"
                    v-model="formData.email"
                    @blur="v$.email.$touch"
                    required="true"
                    class="relative block w-full px-3 py-2 text-gray-900 placeholder-gray-500 border border-gray-300 rounded-none appearance-none rounded-t-md focus:z-10 focus:border-stone-500 focus:outline-none focus:ring-stone-500 sm:text-sm"
                    placeholder="Email address"
                  />
                </div>
                <div>
                  <label for="password" class="sr-only"
                    >Password</label
                  >
                  <input
                    id="password"
                    name="password"
                    type="password"
                    v-model="formData.password"
                    @blur="v$.password.$touch"
                    autocomplete="current-password"
                    required=""
                    class="relative block w-full px-3 py-2 text-gray-900 placeholder-gray-500 border border-gray-300 rounded-none appearance-none rounded-b-md focus:z-10 focus:border-stone-500 focus:outline-none focus:ring-stone-500 sm:text-sm"
                    placeholder="Password"
                  />
                </div>

                <div class="pt-6">
                  <p
                    class="text-red"
                    v-for="error of v$.$errors"
                    :key="error.$uid"
                  >
                    <strong>{{ error.$message }}</strong>
                  </p>
                </div>
              </div>

              <div>
                <button
                  type="submit"
                  class="relative flex justify-center w-full px-4 py-2 text-sm font-medium text-white border border-transparent rounded-md bg-stone-600 group hover:bg-stone-700 focus:outline-none focus:ring-2 focus:ring-stone-500 focus:ring-offset-2"
                >
                  <span
                    class="absolute inset-y-0 left-0 flex items-center pl-3"
                  >
                    <LockClosedIcon
                      class="w-5 h-5 text-stone-500 group-hover:text-stone-400"
                      aria-hidden="true"
                    />
                  </span>
                  Sign in
                </button>
              </div>
            </form>
          </div>
        </div>
      </q-page>
    </q-page-container>
  </q-layout>
</template>

<script setup>
import { LockClosedIcon } from "@heroicons/vue/20/solid";
import { reactive, ref, computed, watch, inject } from "vue";
import { useMutation } from "@vue/apollo-composable";
import gql from "graphql-tag";
import { required, email, helpers } from "@vuelidate/validators";
import { useRouter } from "vue-router";
import useVuelidate from "@vuelidate/core";
import { useQuasar } from "quasar";

const LOGIN = gql`
  mutation login($email: String!, $password: String!) {
    login(email: $email, password: $password) {
      token
      id
    }
  }
`;

const formData = reactive({
  email: "",
  password: "",
});

const rules = {
  email: { email: helpers.withMessage("Email is not valid.", email) },
  password: {
    required: helpers.withMessage("Pasoword is required.", required),
  },
};

const v$ = useVuelidate(rules, formData);

const {
  mutate: sendForm,
  loading: sendFormLoading,
  onDone,
} = useMutation(LOGIN, () => ({
  variables: {
    ...formData,
  },
}));

const submitForm = async () => {
  const isFormCorrect = await v$.value.$validate();
  if (!isFormCorrect) return;
  sendForm();
};

const router = useRouter();
const $q = useQuasar();

onDone((result) => {
  if (result.data.login.token) {
    const value = $q.cookies.set(
      "occusolve-token",
      result.data?.login.token,
      {
        expires: "35h",
        secure: process.env.PROD,
      },
    );
    console.log(value);
    router.push("/");
  } else {
    console.log("Form Error:", result.errors.first.message);
  }
});
</script>
