<template>
  <q-page padding>
    <form @submit.prevent="submitForm">
      <PageHeading class="pb-6 border-b border-stone-400">
        <template #navigation>
          <div class="flex items-center text-gray-500 text-uppercase">
            <ArrowSmallLeftIcon class="w-5 h-5" />
            <router-link to="/patients">Back</router-link>
          </div>
        </template>

        <template #heading> Edit a Patient record </template>

        <template #description>
          Capture the new detail of an existing patient
        </template>

        <template #buttons>
          <q-btn
            flat
            class="text-white bg-teal-700 text-bold"
            padding="md"
            no-caps
            :loading="sendFormLoading"
            type="submit"
          >
            <template v-slot:loading>
              <q-spinner-ios />
            </template>
            <CheckIcon class="w-5 h-5 mr-1 text-bold" />
            <div class="text-sm">Save</div>
          </q-btn>
        </template>
      </PageHeading>

      <div class="max-w-2xl">
        <div
          class="grid grid-cols-1 mt-6 gap-x-4 gap-y-6 sm:grid-cols-6"
        >
          <div class="sm:col-span-4">
            <h3 class="text-lg font-semibold leading-6 text-gray-900">
              Personal Information
            </h3>
          </div>

          <div class="sm:col-span-4">
            <label
              for="identification"
              class="block text-sm font-medium text-stone-400"
            >
              Identification Number*</label
            >
            <div class="mt-1">
              <input
                autocomplete="off"
                type="text"
                v-model="formData.identificationNumber"
                @blur="v$.identificationNumber.$touch"
                name="identification"
                id="identification"
                class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
                placeholder=""
              />
              <div
                class=""
                v-for="error of v$.identificationNumber.$errors"
                :key="error.$uid"
              >
                <div class="text-red-700">{{ error.$message }}</div>
              </div>
            </div>
          </div>

          <div class="sm:col-span-3">
            <label
              for="firstName"
              class="block text-sm font-medium text-stone-400"
              >First Name*</label
            >
            <div class="mt-1">
              <input
                autocomplete="off"
                type="text"
                name="firstName"
                id="firstName"
                v-model="formData.firstName"
                @blur="v$.firstName.$touch"
                class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
                placeholder=""
              />
              <div
                class=""
                v-for="error of v$.firstName.$errors"
                :key="error.$uid"
              >
                <div class="text-red-700">{{ error.$message }}</div>
              </div>
            </div>
          </div>

          <div class="sm:col-span-3">
            <label
              for="lastName"
              class="block text-sm font-medium text-stone-400"
              >Last Name*</label
            >
            <div class="mt-1">
              <input
                autocomplete="off"
                type="text"
                name="lastName"
                id="lastName"
                v-model="formData.lastName"
                @blur="v$.lastName.$touch"
                class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800 placeholder:text-slate-300 placeholder:font-normal"
                placeholder=""
              />
            </div>
            <div
              class=""
              v-for="error of v$.lastName.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>

          <div class="sm:col-span-4">
            <v-date-picker
              color="teal"
              v-model="formData.dob"
              mode="date"
              :masks="masks"
            >
              <template v-slot="{ inputValue, inputEvents }">
                <label
                  for="identification"
                  class="block mb-1 text-sm font-medium text-stone-400"
                >
                  Date of Birth*</label
                >
                <input
                  class="w-full px-2 py-2 font-semibold border rounded-sm shadow-sm border-stone-300 focus:ring-stone-500 focus:border-stone-300 focus:outline-none"
                  :value="inputValue"
                  @blur="v$.dob.$touch"
                  v-on="inputEvents"
                />
              </template>
            </v-date-picker>
            <div
              class=""
              v-for="error of v$.dob.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>

          <div class="sm:col-span-4">
            <div class="flex items-center justify-between">
              <h2 class="text-sm font-medium text-stone-400">
                Gender*
              </h2>
            </div>

            <RadioGroup v-model="formData.gender" class="mt-2">
              <RadioGroupLabel class="sr-only">
                Choose a gender
              </RadioGroupLabel>
              <div class="grid grid-cols-3 gap-3 sm:grid-cols-6">
                <RadioGroupOption
                  as="template"
                  v-for="option in memoryOptions"
                  :key="option.name"
                  :value="option.name"
                  v-slot="{ active, checked }"
                >
                  <div
                    :class="[
                      active
                        ? 'ring-2 ring-offset-2 ring-teal-500'
                        : '',
                      checked
                        ? 'bg-teal-600 border-transparent text-white hover:bg-teal-700'
                        : 'bg-white border-gray-200 text-gray-900 hover:bg-gray-50',
                      'cursor-pointer focus:outline-none border rounded-md py-3 px-3 flex items-center justify-center text-sm font-medium sm:flex-1',
                    ]"
                  >
                    <RadioGroupLabel as="p">
                      {{ option.name }}
                    </RadioGroupLabel>
                  </div>
                </RadioGroupOption>
              </div>
            </RadioGroup>
            <div
              class="mt-1"
              v-for="error of v$.gender.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
          <div class="sm:col-span-4">
            <h3 class="text-lg font-semibold leading-6 text-gray-900">
              Contact Information
            </h3>
          </div>

          <div class="sm:col-span-3">
            <label
              for="phoneNumber"
              class="block text-sm font-medium text-stone-400"
              >Phone Number*</label
            >
            <div class="mt-1">
              <input
                type="tel"
                @blur="v$.phoneNumber.$touch"
                v-model="formData.phoneNumber"
                name="phoneNumber"
                id="phoneNumber"
                autocomplete="off"
                class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
                placeholder=""
                v-mask="'(###) ###-####'"
              />
              <div
                class=""
                v-for="error of v$.phoneNumber.$errors"
                :key="error.$uid"
              >
                <div class="text-red-700">{{ error.$message }}</div>
              </div>
            </div>
          </div>

          <div class="sm:col-span-3">
            <label
              for="email"
              class="block text-sm font-medium text-stone-400"
              >Email</label
            >
            <div class="mt-1">
              <input
                type="email"
                name="email"
                id="email"
                autocomplete="off"
                class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
                v-model="formData.email"
                placeholder=""
              />
              <div
                class=""
                v-for="error of v$.email.$errors"
                :key="error.$uid"
              >
                <div class="text-red-700">{{ error.$message }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </q-page>
</template>

<script setup>
import { useRoute, useRouter } from "vue-router";
import { useQuery, useMutation } from "@vue/apollo-composable";
import { reactive, ref, watch, computed } from "vue";
import gql from "graphql-tag";
import {
  ArrowSmallLeftIcon,
  CheckIcon,
} from "@heroicons/vue/24/outline";
import useVuelidate from "@vuelidate/core";
import { required, email, helpers } from "@vuelidate/validators";
import { logErrorMessages } from "@vue/apollo-util";
const route = useRoute();

const PATIENT = gql`
  query getPatient($id: ID!) {
    patient(id: $id) {
      firstName
      lastName
      dob
      gender
      identificationNumber
      phoneNumber
      email
    }
  }
`;

const { result: patientResult } = useQuery(
  PATIENT,
  { id: route.params.id },
  { fetchPolicy: "cache-and-network" },
);

const memoryOptions = [{ name: "Male" }, { name: "Female" }];
const masks = {
  input: "YYYY/MM/DD",
  mode: "date",
  data: ["L", "YYYY-MM-DD", "YYYY/MM/DD"],
};

const UPDATE_PATIENT = gql`
  mutation updatePatient($id: ID!, $input: PatientInput!) {
    updatePatient(id: $id, input: $input) {
      patient {
        fullName
        id
      }
      success
      errors {
        path
        message
      }
    }
  }
`;

const {
  mutate: sendForm,
  onDone,
  onError,
  loading: sendFormLoading,
} = useMutation(UPDATE_PATIENT, () => ({
  variables: {
    id: route.params.id,
    input: {
      ...formData,
    },
  },
}));

const rules = {
  identificationNumber: {
    required: helpers.withMessage(
      "Identification Number is required.",
      required,
    ),
  },
  firstName: {
    required: helpers.withMessage(
      "First Name is required.",
      required,
    ),
  },
  lastName: {
    required: helpers.withMessage("Last Name is required.", required),
  },
  dob: {
    required: helpers.withMessage(
      "Date of Birth is required.",
      required,
    ),
  },
  gender: {
    required: helpers.withMessage("Gender is required.", required),
  },
  phoneNumber: {
    required: helpers.withMessage("Mobile is required.", required),
  },
  email: { email: helpers.withMessage("Email is not valid.", email) },
};

const formData = reactive({
  identificationNumber: "",
  firstName: "",
  lastName: "",
  dob: "",
  gender: "",
  email: "",
  phoneNumber: "",
});

const patient = computed(() => {
  return patientResult.value?.patient ?? "Patient";
});

watch(
  () => patient.value,
  (outcome) => {
    (formData.dob = outcome.dob),
      (formData.email = outcome.email),
      (formData.firstName = outcome.firstName),
      (formData.lastName = outcome.lastName),
      (formData.gender = outcome.gender),
      (formData.identificationNumber = outcome.identificationNumber),
      (formData.phoneNumber = outcome.phoneNumber);
  },
);

const v$ = useVuelidate(rules, formData);

const submitForm = async () => {
  const isFormCorrect = await v$.value.$validate();
  if (!isFormCorrect) return;
  sendForm();
};

const router = useRouter();

onDone((result) => {
  if (result.data.updatePatient.success == true) {
    router.push(`/patients/${route.params.id}`);
  } else {
    console.log("From Error:", result.data?.updatePatient.success);
  }
});

onError((error) => {
  logErrorMessages(error);
});
</script>
