<template>
<q-page padding>
    <div>
  <form @submit.prevent="submitForm">
    <PageHeading class="pb-6 border-b border-stone-400">
      <template #navigation>
        <div class="flex items-center text-gray-500 text-uppercase">
          <ArrowSmallLeftIcon class="w-5 h-5" />
          <router-link to="/medicals">Back</router-link>
        </div>
      </template>

      <template #heading> Create a medical certificate </template>

      <template #description>
        Capture the detail of a new medical certificate
      </template>

      <template #buttons>
        <q-btn
          flat
          class="text-white bg-teal-700 text-bold"
          padding="md"
          no-caps
          :loading="sendFormLoading"
          type="submit"
        >
          <template v-slot:loading>
            <q-spinner-ios />
          </template>
          <CheckIcon class="w-5 h-5 mr-1 text-bold" />
          <div class="text-sm">Save</div>
        </q-btn>
      </template>
    </PageHeading>

    <div class="max-w-2xl">
      <div
        class="grid grid-cols-1 mt-6 gap-x-4 gap-y-6 sm:grid-cols-6"
      >
        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Patient Information
          </h3>
        </div>

        <div class="sm:col-span-4">
          <div class="mt-1">
            <Listbox
              as="div"
              v-model="selectedPatient"
              @blur="v$.patientId.$touch"
            >
              <ListboxLabel
                class="block text-sm font-medium text-stone-400"
              >
                Patient
              </ListboxLabel>
              <div class="relative mt-1">
                <ListboxButton
                  class="relative w-full py-2 pl-3 pr-10 text-left bg-white border border-gray-300 rounded-sm shadow-sm cursor-default focus:outline-none focus:ring-1 focus:ring-stone-500 focus:border-stone-300 sm:text-sm"
                >
                  <span class="inline-flex w-full truncate">
                    <span class="truncate">{{
                      selectedPatient.fullName
                    }}</span>
                    <span
                      v-if="selectedPatient"
                      class="ml-2 text-gray-500 truncate"
                      >ID:
                      {{ selectedPatient.identificationNumber }}</span
                    >
                  </span>
                  <span
                    class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"
                  >
                    <ChevronUpDownIcon
                      class="w-5 h-5 text-gray-400"
                      aria-hidden="true"
                    />
                  </span>
                </ListboxButton>

                <transition
                  leave-active-class="transition duration-100 ease-in"
                  leave-from-class="opacity-100"
                  leave-to-class="opacity-0"
                >
                  <ListboxOptions
                    class="absolute z-10 w-full py-1 mt-1 overflow-auto text-base bg-white rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
                  >
                    <ListboxOption
                      as="template"
                      v-for="patient in patients"
                      :key="patient.id"
                      :value="patient"
                      v-slot="{ active, selected }"
                    >
                      <li
                        :class="[
                          active
                            ? 'text-white bg-stone-600'
                            : 'text-gray-900',
                          'cursor-default select-none relative py-2 pl-3 pr-9',
                        ]"
                      >
                        <div class="flex">
                          <span
                            :class="[
                              selected
                                ? 'font-semibold'
                                : 'font-normal',
                              'truncate',
                            ]"
                          >
                            {{ patient.fullName }}
                          </span>
                          <span
                            :class="[
                              active
                                ? 'text-stone-200'
                                : 'text-gray-500',
                              'ml-2 truncate',
                            ]"
                          >
                            {{ patient.identificationNumber }}
                          </span>
                        </div>

                        <span
                          v-if="selected"
                          :class="[
                            active ? 'text-white' : 'text-stone-600',
                            'absolute inset-y-0 right-0 flex items-center pr-4',
                          ]"
                        >
                          <CheckIcon
                            class="w-5 h-5"
                            aria-hidden="true"
                          />
                        </span>
                      </li>
                    </ListboxOption>
                  </ListboxOptions>
                </transition>
              </div>
            </Listbox>
            <div
              class=""
              v-for="error of v$.patientId.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-3">
          <div class="mt-1">
            <Listbox as="div" v-model="selectedEmployment">
              <ListboxLabel
                class="block text-sm font-medium text-stone-400"
              >
                Current Employment
              </ListboxLabel>
              <div class="relative mt-1">
                <ListboxButton
                  class="relative w-full py-2 pl-3 pr-10 text-left bg-white border border-gray-300 rounded-sm shadow-sm cursor-default focus:outline-none focus:ring-1 focus:ring-stone-500 focus:border-stone-300 sm:text-sm"
                >
                  <span class="inline-flex w-full truncate">
                    <span class="truncate">{{
                      selectedEmployment.position
                    }}</span>
                    <span class="ml-2 text-gray-500 truncate">{{
                      selectedEmployment.inductionDate
                    }}</span>
                  </span>
                  <span
                    class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"
                  >
                    <SelectorIcon
                      class="w-5 h-5 text-gray-400"
                      aria-hidden="true"
                    />
                  </span>
                </ListboxButton>

                <transition
                  leave-active-class="transition duration-100 ease-in"
                  leave-from-class="opacity-100"
                  leave-to-class="opacity-0"
                >
                  <ListboxOptions
                    class="absolute z-10 w-full py-1 mt-1 overflow-auto text-base bg-white rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
                  >
                    <ListboxOption
                      as="template"
                      v-for="employment in filteredEmployment"
                      :key="employment.id"
                      :value="employment"
                      v-slot="{ active, selected }"
                    >
                      <li
                        :class="[
                          active
                            ? 'text-white bg-stone-600'
                            : 'text-gray-900',
                          'cursor-default select-none relative py-2 pl-3 pr-9',
                        ]"
                      >
                        <div class="flex">
                          <span
                            :class="[
                              selected
                                ? 'font-semibold'
                                : 'font-normal',
                              'truncate',
                            ]"
                          >
                            {{ employment.position }}
                          </span>
                          <span
                            :class="[
                              active
                                ? 'text-stone-200'
                                : 'text-gray-500',
                              'ml-2 truncate',
                            ]"
                          >
                            {{ employment.inductionDate }}
                          </span>
                        </div>

                        <span
                          v-if="selected"
                          :class="[
                            active ? 'text-white' : 'text-stone-600',
                            'absolute inset-y-0 right-0 flex items-center pr-4',
                          ]"
                        >
                          <CheckIcon
                            class="w-5 h-5"
                            aria-hidden="true"
                          />
                        </span>
                      </li>
                    </ListboxOption>
                  </ListboxOptions>
                </transition>
              </div>
            </Listbox>
            <div
              class=""
              v-for="error of v$.employmentId.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-4">
          <div class="mt-1">
            <Listbox as="div" v-model="selectedClinic">
              <ListboxLabel
                class="block text-sm font-medium text-stone-400"
              >
                Clinic Attended
              </ListboxLabel>
              <div class="relative mt-1">
                <ListboxButton
                  class="relative w-full py-2 pl-3 pr-10 text-left bg-white border border-gray-300 rounded-sm shadow-sm cursor-default focus:outline-none focus:ring-1 focus:ring-stone-500 focus:border-stone-300 sm:text-sm"
                >
                  <span class="inline-flex w-full truncate">
                    <span class="truncate">{{
                      selectedClinic.clinicName
                    }}</span></span
                  >
                  <span
                    class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"
                  >
                    <SelectorIcon
                      class="w-5 h-5 text-gray-400"
                      aria-hidden="true"
                    />
                  </span>
                </ListboxButton>

                <transition
                  leave-active-class="transition duration-100 ease-in"
                  leave-from-class="opacity-100"
                  leave-to-class="opacity-0"
                >
                  <ListboxOptions
                    class="absolute z-10 w-full py-1 mt-1 overflow-auto text-base bg-white rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
                  >
                    <ListboxOption
                      as="template"
                      v-for="clinic in clinics"
                      :key="clinic.id"
                      :value="clinic"
                      v-slot="{ active, selected }"
                    >
                      <li
                        :class="[
                          active
                            ? 'text-white bg-stone-600'
                            : 'text-gray-900',
                          'cursor-default select-none relative py-2 pl-3 pr-9',
                        ]"
                      >
                        <div class="flex">
                          <span
                            :class="[
                              selected
                                ? 'font-semibold'
                                : 'font-normal',
                              'truncate',
                            ]"
                          >
                            {{ clinic.clinicName }}
                          </span>
                          <span
                            :class="[
                              active
                                ? 'text-stone-200'
                                : 'text-gray-500',
                              'ml-2 truncate',
                            ]"
                          >
                          </span>
                        </div>

                        <span
                          v-if="selected"
                          :class="[
                            active ? 'text-white' : 'text-stone-600',
                            'absolute inset-y-0 right-0 flex items-center pr-4',
                          ]"
                        >
                          <CheckIcon
                            class="w-5 h-5"
                            aria-hidden="true"
                          />
                        </span>
                      </li>
                    </ListboxOption>
                  </ListboxOptions>
                </transition>
              </div>
            </Listbox>
            <div
              class=""
              v-for="error of v$.clinicId.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Medical Information
          </h3>
        </div>

        <div class="sm:col-span-3">
          <Listbox as="div" v-model="selectedMedicalType">
            <ListboxLabel
              class="block text-sm font-medium text-gray-700"
            >
              Medical Type
            </ListboxLabel>
            <div class="relative mt-1">
              <ListboxButton
                class="relative w-full py-2 pl-3 pr-10 text-left bg-white border border-gray-300 rounded-sm shadow-sm cursor-default focus:outline-none focus:ring-1 focus:ring-stone-500 focus:border-stone-300 sm:text-sm"
              >
                <span class="block truncate">{{
                  selectedMedicalType.name
                }}</span>
                <span
                  class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"
                >
                  <SelectorIcon
                    class="w-5 h-5 text-gray-400"
                    aria-hidden="true"
                  />
                </span>
              </ListboxButton>

              <transition
                leave-active-class="transition duration-100 ease-in"
                leave-from-class="opacity-100"
                leave-to-class="opacity-0"
              >
                <ListboxOptions
                  class="absolute z-10 w-full py-1 mt-1 overflow-auto text-base bg-white rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
                >
                  <ListboxOption
                    as="template"
                    v-for="type in evaluationTypes"
                    :key="type.name"
                    :value="type"
                    v-slot="{ active, selected }"
                  >
                    <li
                      :class="[
                        active
                          ? 'text-white bg-stone-600'
                          : 'text-gray-900',
                        'cursor-default select-none relative py-2 pl-3 pr-9',
                      ]"
                    >
                      <span
                        :class="[
                          selected ? 'font-semibold' : 'font-normal',
                          'block truncate',
                        ]"
                      >
                        {{ type.name }}
                      </span>

                      <span
                        v-if="selected"
                        :class="[
                          active ? 'text-white' : 'text-stone-600',
                          'absolute inset-y-0 right-0 flex items-center pr-4',
                        ]"
                      >
                        <CheckIcon
                          class="w-5 h-5"
                          aria-hidden="true"
                        />
                      </span>
                    </li>
                  </ListboxOption>
                </ListboxOptions>
              </transition>
            </div>
          </Listbox>
          <div
            v-for="error of v$.medicalType.$errors"
            :key="error.$uid"
          >
            <div class="text-red-700">{{ error.$message }}</div>
          </div>
        </div>

        <div class="sm:col-span-3">
          <div class="mt-1">
            <div>
              <label
                for="medicalRef"
                class="block text-sm font-medium text-gray-700"
                >Medical Reference</label
              >
              <div class="relative mt-1 rounded-md shadow-sm">
                <div
                  class="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none"
                >
                  <span class="text-gray-500 sm:text-sm">
                    Ref -
                  </span>
                </div>
                <input
                  v-model="formData.name"
                  type="text"
                  name="medicalRef"
                  id="medicalRef"
                  class="block w-full pl-16 border-gray-300 rounded-sm text-uppercase focus:ring-stone-500 focus:border-stone-500 sm:pl-14 sm:text-sm"
                  placeholder=""
                />
              </div>
              <div v-for="error of v$.name.$errors" :key="error.$uid">
                <div class="text-red-700">{{ error.$message }}</div>
              </div>
            </div>
          </div>
        </div>

        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Medical Investigations Performed
          </h3>
          <p class="text-sm leading-5 text-gray-500">
            How do you prefer to add the employing company?
          </p>
        </div>
        <div class="sm:col-span-4">
          <fieldset class="space-y-4">
            <legend class="sr-only">Medical Investigations</legend>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  v-model="formData.physicalExamPerformed"
                  id="physicalExamination"
                  aria-describedby="physicalExamination performed"
                  name="physicalExamination"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="physicalExamination"
                  class="font-medium text-gray-700"
                  >Physical Examination</label
                >
              </div>
            </div>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  v-model="formData.visionScreeningPerformed"
                  id="visionScreeening"
                  aria-describedby="visionScreeening"
                  name="visionScreeening"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="visionScreeening"
                  class="font-medium text-gray-700"
                  >Vision Screening</label
                >
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  v-model="formData.audioPerformed"
                  id="audiometry"
                  aria-describedby="audiometry"
                  name="audiometry"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="audiometry"
                  class="font-medium text-gray-700"
                  >Audiometry</label
                >
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  v-model="formData.spiroPerformed"
                  id="spirometry"
                  aria-describedby="spirometry"
                  name="spirometry"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="spirometry"
                  class="font-medium text-gray-700"
                  >Spirometry</label
                >
              </div>
            </div>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  v-model="formData.xrayPerformed"
                  id="xray"
                  aria-describedby="xray"
                  name="xray"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label for="xray" class="font-medium text-gray-700"
                  >Chest X Ray</label
                >
              </div>
            </div>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  v-model="formData.ecgPerformed"
                  id="restingEcg"
                  aria-describedby="restingEcg"
                  name="restingEcg"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="restingEcg"
                  class="font-medium text-gray-700"
                  >Resting ECG</label
                >
              </div>
            </div>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  v-model="formData.heatPerformed"
                  id="heatStress"
                  aria-describedby="heatStress"
                  name="heatStress"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="heatStress"
                  class="font-medium text-gray-700"
                  >Heat Stress</label
                >
              </div>
            </div>
            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  v-model="formData.heightPerformed"
                  id="workAtHeight"
                  aria-describedby="workAtHeight"
                  name="workAtHeight"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="workAtHeight"
                  class="font-medium text-gray-700"
                  >Work At Height Assessment</label
                >
              </div>
            </div>

            <div class="relative flex items-start">
              <div class="flex items-center h-5">
                <input
                  v-model="formData.cannabisPerformed"
                  id="drugScreening"
                  aria-describedby="drugScreening"
                  name="drugScreening"
                  type="checkbox"
                  class="w-4 h-4 border-gray-300 rounded focus:ring-stone-500 text-stone-600"
                />
              </div>
              <div class="ml-3 text-sm">
                <label
                  for="drugScreening"
                  class="font-medium text-gray-700"
                  >Drug Screening</label
                >
              </div>
            </div>
          </fieldset>
        </div>

        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Results
          </h3>
        </div>

        <div class="sm:col-span-5">
          <label
            for="conclusion"
            class="block text-sm font-medium text-stone-400"
            >Conclusion</label
          >
          <div class="mt-1">
            <Listbox as="div" v-model="selectedOutcome">
              <ListboxLabel class="sr-only">
                Change medical outcome
              </ListboxLabel>
              <div class="relative">
                <div
                  class="inline-flex divide-x rounded-md shadow-sm divide-stone-600"
                >
                  <div
                    class="relative z-0 inline-flex divide-x rounded-md shadow-sm divide-stone-600"
                  >
                    <div
                      class="relative inline-flex items-center py-2 pl-3 pr-4 text-white border border-transparent shadow-sm bg-stone-600 rounded-l-md"
                    >
                      <CheckIcon class="w-5 h-5" aria-hidden="true" />
                      <p class="ml-2.5 text-sm font-medium">
                        {{ selectedOutcome.heading }}
                      </p>
                    </div>
                    <ListboxButton
                      class="relative inline-flex items-center p-2 text-sm font-medium text-white rounded-l-none bg-stone-600 rounded-r-md hover:bg-stone-600 focus:outline-none focus:z-10 focus:ring-2 focus:ring-offset-2 focus:ring-offset-gray-50 focus:ring-stone-500"
                    >
                      <span class="sr-only"
                        >Change published status</span
                      >
                      <ChevronDownIcon
                        class="w-5 h-5 text-white"
                        aria-hidden="true"
                      />
                    </ListboxButton>
                  </div>
                </div>

                <transition
                  leave-active-class="transition duration-100 ease-in"
                  leave-from-class="opacity-100"
                  leave-to-class="opacity-0"
                >
                  <ListboxOptions
                    class="absolute left-0 z-10 mt-2 overflow-hidden origin-top-left bg-white divide-y divide-gray-200 rounded-sm shadow-lg w-72 ring-1 ring-black ring-opacity-5 focus:outline-none"
                  >
                    <ListboxOption
                      as="template"
                      v-for="option in outcomes"
                      :key="option.heading"
                      :value="option"
                      v-slot="{ active, selected }"
                    >
                      <li
                        :class="[
                          active
                            ? 'text-white bg-stone-600'
                            : 'text-gray-900',
                          'cursor-default select-none relative p-4 text-sm',
                        ]"
                      >
                        <div class="flex flex-col">
                          <div class="flex justify-between">
                            <p
                              :class="
                                selected
                                  ? 'font-semibold'
                                  : 'font-normal'
                              "
                            >
                              {{ option.heading }}
                            </p>
                            <span
                              v-if="selected"
                              :class="
                                active
                                  ? 'text-white'
                                  : 'text-stone-600'
                              "
                            >
                              <CheckIcon
                                class="w-5 h-5"
                                aria-hidden="true"
                              />
                            </span>
                          </div>
                          <p
                            :class="[
                              active
                                ? 'text-stone-200'
                                : 'text-gray-500',
                              'mt-2',
                            ]"
                          >
                            {{ option.body }}
                          </p>
                        </div>
                      </li>
                    </ListboxOption>
                  </ListboxOptions>
                </transition>
              </div>
            </Listbox>
          </div>
        </div>
        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Validity
          </h3>
        </div>

        <div class="sm:col-span-3">
          <v-date-picker
            color="teal"
            v-model="formData.medicalExaminationDate"
            :masks="masks"
            class=""
          >
            <template v-slot="{ inputValue, togglePopover }">
              <label
                for="employment_start_date"
                class="block mb-1 text-sm font-medium text-stone-400"
                >Date of the Medical Examination</label
              >
              <input
                class="w-full px-2 py-2 font-semibold border rounded-sm shadow-sm border-stone-300 focus:border-stone-500 focus:outline-none focus:ring-1 focus:ring-stone-500"
                :value="inputValue"
                @click="togglePopover"
              />
            </template>
          </v-date-picker>
        </div>

        <div class="sm:col-span-3">
          <v-date-picker
            color="teal"
            v-model="formData.medicalExpiryDate"
            :masks="masks"
            class=""
          >
            <template v-slot="{ inputValue, togglePopover }">
              <label
                for="employment_start_date"
                class="block mb-1 text-sm font-medium text-stone-400"
                >Expiry date of the medical</label
              >
              <input
                class="w-full px-2 py-2 font-semibold border rounded-sm shadow-sm border-stone-300 focus:border-stone-500 focus:outline-none focus:ring-1 focus:ring-stone-500"
                :value="inputValue"
                @click="togglePopover"
              />
            </template>
          </v-date-picker>
        </div>

        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Final Comment
          </h3>
          <p class="text-sm leading-5 text-gray-500">
            Any comments to appear on this patient's assessment?
          </p>
        </div>

        <div class="sm:col-span-4">
          <fieldset class="mt-2">
            <legend class="sr-only">Final Commentary</legend>
            <div class="space-y-4">
              <div class="flex items-center">
                <input
                  id="noFInalComment"
                  v-model="formData.outcomeComment"
                  value="false"
                  name="noFInalComment"
                  type="radio"
                  class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                />
                <div>
                  <label
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    No comment required
                  </label>
                </div>
              </div>

              <div class="flex items-center">
                <input
                  id="CommentCapture"
                  v-model="formData.outcomeComment"
                  value="true"
                  name="finalComment"
                  type="radio"
                  class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                />
                <div>
                  <label
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Capture a comment
                  </label>
                </div>
              </div>
              <div
                v-if="formData.outcomeComment == 'true'"
                class="ml-7"
              >
                <div class="mt-1">
                  <textarea
                    v-model="formData.outcomeCommentText"
                    rows="3"
                    name="comment"
                    id="comment"
                    class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-stone-500 focus:border-stone-500 sm:text-sm"
                  />
                </div>
              </div>
            </div>
          </fieldset>
        </div>

        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Referral
          </h3>
          <p class="text-sm leading-5 text-gray-500">
            Does this patient need to be refered to an specialist?
          </p>
        </div>

        <div class="sm:col-span-4">
          <fieldset class="mt-2">
            <legend class="sr-only">Referral Requirement</legend>
            <div class="space-y-4">
              <div class="flex items-center">
                <input
                  id="noReferrral"
                  v-model="formData.referral"
                  value="false"
                  name="noReferrral"
                  type="radio"
                  class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                />
                <div>
                  <label
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    No referral required
                  </label>
                </div>
              </div>

              <div class="flex items-center">
                <input
                  id="ReferralCapture"
                  v-model="formData.referral"
                  value="true"
                  name="ReferralCapture"
                  type="radio"
                  class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                />
                <div>
                  <label
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Capture a referral
                  </label>
                </div>
              </div>
              <div v-if="formData.referral == 'true'" class="ml-7">
                <div class="mt-1">
                  <textarea
                    v-model="formData.referralComment"
                    rows="3"
                    name="comment"
                    id="comment"
                    class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-stone-500 focus:border-stone-500 sm:text-sm"
                  />
                </div>
              </div>
            </div>
          </fieldset>
        </div>

        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Exclusions
          </h3>
          <p class="text-sm leading-5 text-gray-500">
            What exclusions do think this patient needs?
          </p>
        </div>

        <div class="sm:col-span-4">
          <fieldset class="mt-2">
            <legend class="sr-only">Exclusions Requirement</legend>
            <div class="space-y-4">
              <div class="flex items-center">
                <input
                  id="noExclusion"
                  v-model="formData.exclusion"
                  value="false"
                  name="noExclusion"
                  type="radio"
                  class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                />
                <div>
                  <label
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    No exclusions required
                  </label>
                </div>
              </div>

              <div class="flex items-center">
                <input
                  id="exclusionCapture"
                  v-model="formData.exclusion"
                  value="true"
                  name="exclusionCapture"
                  type="radio"
                  class="w-4 h-4 border-gray-300 focus:ring-stone-500 text-stone-600"
                />
                <div>
                  <label
                    class="block ml-3 text-sm font-medium text-gray-700"
                  >
                    Capture an exclusion
                  </label>
                </div>
              </div>
              <div v-if="formData.exclusion == 'true'" class="ml-7">
                <div class="mt-1">
                  <textarea
                    v-model="formData.exclusionComment"
                    rows="3"
                    name="ExclusionsComment"
                    id="ExclusionsComment"
                    class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-stone-500 focus:border-stone-500 sm:text-sm"
                  />
                </div>
              </div>
            </div>
          </fieldset>
        </div>
      </div>
    </div>
  </form>
    </div>
    </q-page>
</template>

<script setup>
import { ref, reactive, watch, computed } from "vue";
import { date } from "quasar";
import { useRouter } from "vue-router";
import useVuelidate from "@vuelidate/core";
import {
  CheckIcon,
  ChevronUpDownIcon,
  ChevronDownIcon,
  ArrowSmallLeftIcon,
} from "@heroicons/vue/24/solid";
import { useQuery, useMutation } from "@vue/apollo-composable";
import gql from "graphql-tag";
import { required, helpers } from "@vuelidate/validators";

const CAPTURE_MEDICAL_FORM = gql`
  mutation createMedical($input: MedicalCaptureFormInput!) {
    createMedicalCaptureForm(input: $input) {
      success
      errors {
        path
        message
      }
      medical {
        status
      }
    }
  }
`;

const PATIENTS = gql`
  query listPatients($id: ID!) {
    patients(organisationId: $id) {
      id
      identificationNumber
      fullName
      employments {
        company {
          name
        }
        id
        inductionDate
        position
      }
    }
  }
`;

const CLINICS = gql`
  query listClinics($id: ID!) {
    clinics(organisationId: $id) {
      id
      clinicName
    }
  }
`;

const { result: clinicResult } = useQuery(
  CLINICS,
  { id: 1 },
  { fetchPolicy: "cache-and-network" },
);

const { result: patientResult } = useQuery(
  PATIENTS,
  { id: 1 },
  { fetchPolicy: "cache-and-network" },
);

const clinics = computed(() => clinicResult?.value.clinics ?? []);
const patients = computed(() => patientResult?.value.patients ?? []);

const masks = {
  input: "YYYY/MM/DD",
  mode: "date",
  data: ["L", "YYYY-MM-DD", "YYYY/MM/DD"],
};

const outcomes = [
  {
    heading: "Fit",
    body: "Fit, Mentally and Physically to perform duties as per job description",
  },
  {
    heading: "Not Fit",
    body: "Not fit to perform durites as per job description",
  },
  {
    heading: "Pending",
    body: "Fitness pending on further investigation",
  },
  { heading: "Exclusion", body: "Fit, with noted exclusion(s)" },
];

const evaluationTypes = [
  { name: "Pre-Employment" },
  { name: "Bi-Annual" },
  { name: "Annual" },
  { name: "Exit" },
];

const todayDate = new Date();
const nextYear = date.addToDate(todayDate, { year: 1 });

const formData = reactive({
  patientId: "",
  employmentId: "",
  clinicId: "",

  medicalType: evaluationTypes[0].name,
  name: "",

  physicalExamPerformed: false,
  visionScreeningPerformed: false,
  audioPerformed: false,
  spiroPerformed: false,
  xrayPerformed: false,
  ecgPerformed: false,
  heatPerformed: false,
  heightPerformed: false,
  cannabisPerformed: false,

  outcome: outcomes[0].body,
  outcomeComment: false,
  outcomeCommentText: "",

  referral: false,
  referralComment: "",
  exclusion: false,
  exclusionComment: "",
  medicalExaminationDate: todayDate,
  medicalExpiryDate: nextYear,
});

const {
  mutate: sendForm,
  onDone,
  loading: sendFormLoading,
} = useMutation(CAPTURE_MEDICAL_FORM, () => ({
  variables: {
    input: {
      ...formData,
    },
  },
}));

const router = useRouter();

onDone((result) => {
  if (result.data.createMedicalCaptureForm.success == true) {
    resetForm();
    router.push("/medicals");
  } else {
    console.log(
      "From Error:",
      result.data?.createMedicalCaptureForm.success,
    );
  }
});

const selectedOutcome = ref(outcomes[0]);
const selectedPatient = ref("");
let selectedEmployment = ref("");
const selectedMedicalType = ref(evaluationTypes[0]);
const selectedClinic = ref("");

const filteredEmployment = computed(() =>
  selectedPatient.value === ""
    ? []
    : selectedPatient.value?.employments,
);

watch(
  () => selectedPatient.value,
  (outcome) => {
    formData.patientId = outcome.id;
    selectedEmployment.value = "";
  },
);

watch(
  () => selectedEmployment.value,
  (outcome) => {
    formData.employmentId = outcome.id;
  },
);

watch(
  () => selectedOutcome.value,
  (outcome) => {
    formData.outcome = outcome.body;
  },
);
watch(
  () => selectedClinic.value,
  (outcome) => {
    formData.clinicId = outcome.id;
  },
);

watch(
  () => selectedMedicalType.value,
  (outcome) => {
    formData.name = outcome.name;
  },
);

const rules = {
  patientId: {
    required: helpers.withMessage("Patient is required.", required),
  },
  employmentId: {
    required: helpers.withMessage(
      "Employment information is required.",
      required,
    ),
  },
  clinicId: {
    required: helpers.withMessage(
      "Clinic information is required.",
      required,
    ),
  },
  medicalType: {
    required: helpers.withMessage(
      "Medical Type is required.",
      required,
    ),
  },
  name: {
    required: helpers.withMessage(
      "Medical Reference is required.",
      required,
    ),
  },
};

const v$ = useVuelidate(rules, formData);

const resetForm = () => {
  formData.patientId = "";
  formData.employmentId = "";
  formData.clinicId = "";
  formData.medicalType = evaluationTypes[0].name;
  formData.name = "";
  formData.physicalExamPerformed = "";
  formData.visionScreeningPerformed = "";
  formData.audioPerformed = "";
  formData.spiroPerformed = "";
  formData.xrayPerformed = "";
  formData.ecgPerformed = "";
  formData.heatPerformed = "";
  formData.heightPerformed = "";
  formData.cannabisPerformed = "";
  formData.outcome = outcomes[0].body;
  formData.outcomeComment = "false";
  formData.outcomeCommentText = "";
  formData.referral = "false";
  formData.referralComment = "";
  formData.exclusion = "false";
  formData.exclusionComment = "";
  formData.medicalExaminationDate = todayDate;
  formData.medicalExpiryDate = nextYear;
};

const submitForm = async () => {
  const isFormCorrect = await v$.value.$validate();
  if (!isFormCorrect) return;
  sendForm();
};
</script>
