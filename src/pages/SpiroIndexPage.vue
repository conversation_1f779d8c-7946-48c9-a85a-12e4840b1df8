<template>
  <q-page padding>
    <div class="flex content-center justify-center">
      <div
        v-if="result && result.spiros && documentCount == 0"
        class="w-full"
      >
        <PageHeading class="pb-6">
          <template #heading> Spirometry Assessments </template>

          <template #navigation>
            <div
              class="flex items-center text-gray-500 text-uppercase"
            >
              <router-link
                class="flex"
                :to="{
                  name: 'assessments',
                  params: { id: route.params.patient_id },
                }"
              >
                <ArrowLeftIcon class="w-5 h-5 mr-1" />
                Back
              </router-link>
            </div>
          </template>
          <template #description>
            {{ documentCount }} document records
          </template>
          <template #buttons>
            <q-btn
              flat
              class="text-white bg-teal-700 text-bold"
              padding="md"
              no-caps
              :to="{
                name: 'new_spiro',
                params: { patient_id: route.params.patient_id },
              }"
            >
              <PlusIcon class="w-5 h-5 mr-1 text-bold" />
              <div class="text-sm">Spirometry</div>
            </q-btn>
          </template>
        </PageHeading>
        <div class="flex justify-center h-full">
          <EmptyState />
        </div>
      </div>
      <div
        v-else-if="result && result.spiros && documentCount > 0"
        class="w-full"
      >
        <PageHeading class="pb-6">
          <template #heading> Spirometry Assessment</template>
          <template #navigation>
            <div
              class="flex items-center text-gray-500 text-uppercase"
            >
              <router-link
                class="flex"
                :to="{
                  name: 'assessments',
                  params: { id: route.params.patient_id },
                }"
              >
                <ArrowLeftIcon class="w-5 h-5 mr-1" />
                Back
              </router-link>
            </div>
          </template>
          <template #description>
            {{ documentCount }} document records
          </template>
          <template #buttons>
            <q-btn
              flat
              class="text-white bg-teal-700 text-bold"
              padding="md"
              no-caps
              :to="{
                name: 'new_spiro',
                params: { patient_id: route.params.patient_id },
              }"
            >
              <PlusIcon class="w-5 h-5 mr-1 text-bold" />
              <div class="text-sm">Spirometry</div>
            </q-btn>
          </template>
        </PageHeading>

        <div class="px-4 sm:px-6 lg:px-8">
          <div class="flex flex-col mt-8">
            <div
              class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"
            >
              <div
                class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8"
              >
                <div
                  class="overflow-hidden shadow ring-0.5 ring-stone-50 ring-opacity-5 md:rounded-sm"
                >
                  <table class="min-w-full divide-y divide-gray-100">
                    <thead class="">
                      <tr>
                        <th
                          scope="col"
                          class="py-3 pl-4 pr-3 text-xs font-medium tracking-wide text-left text-gray-500 uppercase sm:pl-6"
                        >
                          Document Title
                        </th>
                        <th
                          scope="col"
                          class="px-3 py-3 text-xs font-medium tracking-wide text-left text-gray-500 uppercase"
                        >
                          Result
                        </th>
                        <th
                          scope="col"
                          class="px-3 py-3 text-xs font-medium tracking-wide text-left text-gray-500 uppercase"
                        >
                          Attachments
                        </th>
                        <th
                          scope="col"
                          class="relative py-3 pl-3 pr-4 sm:pr-6"
                        >
                          <span class="sr-only">Edit</span>
                        </th>
                      </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-100">
                      <tr v-for="doc in result.spiros" :key="doc.id">
                        <td
                          class="py-4 pl-4 pr-3 text-sm font-medium whitespace-nowrap text-stone-900 sm:pl-6"
                        >
                          {{ doc.name }}
                        </td>
                        <td
                          class="px-3 py-4 text-sm whitespace-nowrap text-stone-500"
                        >
                          {{
                            doc.result
                              ? doc.result
                              : "-- no details provided --"
                          }}
                        </td>
                        <td
                          class="px-3 py-4 text-sm whitespace-nowrap text-stone-500"
                        >
                          <ul>
                            <li
                              v-for="att in doc.attachments"
                              :key="att.id"
                            >
                              <a :href="att.url" target="_blank">
                                <div
                                  class="flex items-center justify-start px-1 py-2 hover:text-teal-800"
                                >
                                  <div>
                                    {{ att.fileName }}
                                  </div>
                                  <div>
                                    <PaperClipIcon
                                      class="w-4 h-4 ml-4"
                                    />
                                  </div>
                                </div>
                              </a>
                            </li>
                          </ul>
                        </td>
                        <td
                          class="relative flex py-4 pl-3 pr-4 text-sm font-medium text-left whitespace-nowrap sm:pr-6"
                        >
                          <router-link
                            :to="{
                              name: 'show_spiro',
                              params: { id: doc.id },
                            }"
                          >
                            <div class="flex items-center">
                              Show
                              <ArrowRightIcon
                                class="w-4 h-4 ml-1 text-stone-400 hover:text-teal-900"
                              />
                            </div>
                          </router-link>
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div></div>
  </q-page>
</template>

<script setup>
import {
  PlusIcon,
  ArrowRightIcon,
  PaperClipIcon,
  ArrowLeftIcon,
} from "@heroicons/vue/24/outline";
import { useQuery } from "@vue/apollo-composable";
import gql from "graphql-tag";
import { computed } from "vue";
import { useRoute } from "vue-router";

const route = useRoute();

let documentCount = computed(() => {
  return result?.value.spiros?.length;
});

const { result } = useQuery(
  gql`
    query getSpiros($id: ID!) {
      spiros(patientId: $id) {
        id
        name
        result
        attachments {
          id
          url
          fileName
        }
      }
    }
  `,
  {
    id: route.params.patient_id,
  },
  {
    fetchPolicy: "cache-and-network",
  },
);
</script>
