<template>
  <q-page padding>
    <form @submit.prevent="submitForm" class="max-w-3xl">
      <PageHeading>
        <template #navigation>
          <div class="flex items-center text-gray-500 text-uppercase">
            <router-link
              class="flex"
              :to="{
                name: 'assessments',
                params: { patient_id: route.params.patient_id },
              }"
            >
              <ArrowSmallLeftIcon class="w-5 h-5" />
              Back
            </router-link>
          </div>
        </template>
        <template #heading>
          <div class="capitalize">New Annexure Three</div>
        </template>

        <template #buttons>
          <q-btn
            flat
            class="text-white bg-teal-700 text-bold"
            padding="md"
            no-caps
            :loading="sendFormLoading"
            type="submit"
          >
            <template v-slot:loading>
              <q-spinner-ios />
            </template>
            <CheckIcon class="w-5 h-5 mr-1 font-bold" />
            <div class="text-sm">Save</div>
          </q-btn>
        </template>
      </PageHeading>
    </form>

    <div
      class="flex flex-col max-w-3xl p-4 rounded-sm shadow md:mt-8 bg-stone-50"
    >
      <div class="max-w-4xl">
        <div class="grid grid-cols-2 gap-4">
          <div class="flex flex-col">
            <div
              class="mb-1 text-xs font-bold uppercase text-stone-400"
            >
              Patient fullname
            </div>
            <div class="font-bold">
              Jackie Roeloef
              <!-- {{ company.email }} -->
            </div>
          </div>
          <div class="flex flex-col">
            <div
              class="mb-1 text-xs font-bold uppercase text-stone-400"
            >
              ID Number
            </div>
            <div class="font-bold">
              123123123123
              <!-- {{ company.phoneNumber }} -->
            </div>
          </div>

          <div class="flex flex-col">
            <div
              class="mb-1 text-xs font-bold uppercase text-stone-400"
            >
              Employment Number
            </div>
            <div class="font-bold">
              PHA123S4
              <!-- {{ company.phoneNumber }} -->
            </div>
          </div>

          <div class="flex flex-col">
            <div
              class="mb-1 text-xs font-bold uppercase text-stone-400"
            >
              Occupation
            </div>
            <div class="font-bold">
              Site Manager
              <!-- {{ company.phoneNumber }} -->
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-2xl">
      <div
        class="grid grid-cols-1 mt-6 gap-x-4 gap-y-6 sm:grid-cols-6"
      >
        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Possible Exposures
          </h3>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 1</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <!-- v-model="formData.name"
              @blur="v$.name.$touch" -->

            <!-- <div
              class=""
              v-for="error of v$.name.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div> -->
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 2</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <!-- v-model="formData.name"
              @blur="v$.name.$touch" -->

            <!-- <div
              class=""
              v-for="error of v$.name.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div> -->
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 3</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 4</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <!-- v-model="formData.name"
              @blur="v$.name.$touch" -->

            <!-- <div
              class=""
              v-for="error of v$.name.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div> -->
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 5</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 6</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <!-- v-model="formData.name"
              @blur="v$.name.$touch" -->

            <!-- <div
              class=""
              v-for="error of v$.name.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div> -->
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 7</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <!-- v-model="formData.name"
              @blur="v$.name.$touch" -->

            <!-- <div
              class=""
              v-for="error of v$.name.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div> -->
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 8</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <!-- v-model="formData.name"
              @blur="v$.name.$touch" -->

            <!-- <div
              class=""
              v-for="error of v$.name.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div> -->
          </div>
        </div>

        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Job Specific Requirements
          </h3>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 1</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <!-- v-model="formData.name"
              @blur="v$.name.$touch" -->

            <!-- <div
              class=""
              v-for="error of v$.name.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div> -->
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 2</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <!-- v-model="formData.name"
              @blur="v$.name.$touch" -->

            <!-- <div
              class=""
              v-for="error of v$.name.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div> -->
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 3</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 4</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <!-- v-model="formData.name"
              @blur="v$.name.$touch" -->

            <!-- <div
              class=""
              v-for="error of v$.name.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div> -->
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 5</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 6</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <!-- v-model="formData.name"
              @blur="v$.name.$touch" -->

            <!-- <div
              class=""
              v-for="error of v$.name.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div> -->
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 7</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <!-- v-model="formData.name"
              @blur="v$.name.$touch" -->

            <!-- <div
              class=""
              v-for="error of v$.name.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div> -->
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 8</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <!-- v-model="formData.name"
              @blur="v$.name.$touch" -->

            <!-- <div
              class=""
              v-for="error of v$.name.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div> -->
          </div>
        </div>

        <div class="sm:col-span-4">
          <h3 class="text-lg font-semibold leading-6 text-gray-900">
            Protective Equipment
          </h3>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 1</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <!-- v-model="formData.name"
              @blur="v$.name.$touch" -->

            <!-- <div
              class=""
              v-for="error of v$.name.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div> -->
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 2</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <!-- v-model="formData.name"
              @blur="v$.name.$touch" -->

            <!-- <div
              class=""
              v-for="error of v$.name.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div> -->
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 3</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 4</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <!-- v-model="formData.name"
              @blur="v$.name.$touch" -->

            <!-- <div
              class=""
              v-for="error of v$.name.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div> -->
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 5</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
          </div>
        </div>

        <div class="sm:col-span-4">
          <label
            for="name"
            class="block text-sm font-medium text-stone-400"
          >
            Exposure Name 6</label
          >
          <div class="mt-1">
            <input
              autocomplete="off"
              type="text"
              name="name"
              id="name"
              class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
              placeholder=""
            />
            <!-- v-model="formData.name"
              @blur="v$.name.$touch" -->

            <!-- <div
              class=""
              v-for="error of v$.name.$errors"
              :key="error.$uid"
            >
              <div class="text-red-700">{{ error.$message }}</div>
            </div> -->
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import vueFilePond from "vue-filepond";
import {
  ArrowSmallLeftIcon,
  CheckIcon,
} from "@heroicons/vue/24/solid";
import gql from "graphql-tag";
import { useQuery, useMutation } from "@vue/apollo-composable";
import { useRoute, useRouter } from "vue-router";
import useVuelidate from "@vuelidate/core";
import { required, helpers } from "@vuelidate/validators";
import "filepond/dist/filepond.min.css";
import { ref, reactive, watch, computed } from "vue";

import FilePondPluginFileValidateType from "filepond-plugin-file-validate-type";

const route = useRoute();
const FilePond = vueFilePond(FilePondPluginFileValidateType);
const CREATE_PATIENT_DOCUMENT = gql`
  mutation createPatientDocument(
    $id: ID!
    $input: PatientDocumentInput!
  ) {
    createPatientDocument(patientId: $id, input: $input) {
      patientDocument {
        id
        name
        attachments {
          url
          id
        }
      }
      success
      errors {
        path
        message
      }
    }
  }
`;

const {
  mutate: sendForm,
  onDone,
  loading: sendFormLoading,
} = useMutation(CREATE_PATIENT_DOCUMENT, () => ({
  variables: {
    id: route.params.patient_id,
    input: {
      ...formData,
    },
  },
}));

const rules = {
  name: {
    required: helpers.withMessage(
      "Document title is required.",
      required,
    ),
  },
  attachments: {
    required: helpers.withMessage(
      "Attachments can't be empty.",
      required,
    ),
  },
};

const formData = reactive({
  name: "",
  description: "",
  attachments: [],
});

const addFile = (error, file) => {
  formData.attachments = [...formData.attachments, file.file];
};
const removeFile = (error, file) => {
  const result = formData.attachments.filter((item) => {
    return item.id != file.id;
  });

  formData.attachments = [...result];
};
const v$ = useVuelidate(rules, formData);

const submitForm = async () => {
  const isFormCorrect = await v$.value.$validate();
  console.log(formData);
  if (!isFormCorrect) return;
  sendForm();
};

const router = useRouter();

onDone((result) => {
  if (result.data.createPatientDocument.success == true) {
    router.push({
      name: "patient_documents",
      params: { id: route.params.patient_id },
    });
  } else
    console.log(
      "From Error:",
      result.data?.createPatientDocument.success,
    );
});
const selectedClinic = ref("");

watch(
  () => selectedClinic.value,
  (outcome) => {
    formData.clinicId = outcome.id;
  },
);

const CLINICS = gql`
  query listClinics($id: ID!) {
    clinics(organisationId: $id) {
      id
      clinicName
    }
  }
`;

const { result: clinicResult } = useQuery(
  CLINICS,
  { id: 1 },
  { fetchPolicy: "cache-and-network" },
);
</script>
