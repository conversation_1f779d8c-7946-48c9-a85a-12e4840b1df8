<template>
  <q-page padding>
    <PageHeading>
      <template #navigation>
        <div class="flex items-center text-gray-500 text-uppercase">
          <ArrowSmallLeftIcon class="w-5 h-5" />
          <router-link to="/companies">Back</router-link>
        </div>
      </template>
      <template #heading>
        <div class="capitalize">
          {{ company.name }}
        </div>
      </template>
    </PageHeading>
    <div class="max-w-4xl">
      <div
        class="flex flex-col p-4 rounded-sm shadow md:mt-8 bg-stone-50"
      >
        <div class="flex justify-between">
          <div class="mb-6 font-bold text-stone-700">Details</div>

          <span class="justify-end rounded-md">
            <router-link
              class="inline-flex items-center px-2.5 py-1.5 border border-gray-300 text-xs leading-4 font-medium rounded text-stone-100 bg-stone-800 hover:text-stone-100 focus:outline-none focus:border-blue-300 focus:shadow-outline-blue active:text-gray-800 active:bg-gray-50 transition ease-in-out duration-150"
              :to="{ name: 'edit_company', id: route.params.id }"
            >
              Edit Company
            </router-link>
          </span>
        </div>
        <div class="max-w-4xl">
          <div class="grid grid-cols-2 gap-4">
            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-400"
              >
                Email
              </div>
              <div class="font-bold">
                {{ company.email }}
              </div>
            </div>
            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-400"
              >
                Phone Number
              </div>
              <div class="font-bold">
                {{ company.phoneNumber }}
              </div>
            </div>

            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-400"
              >
                Street Address
              </div>
              <div class="font-bold capitalize">
                {{ company.streetAddress }}
              </div>
            </div>

            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-400"
              >
                City
              </div>
              <div class="font-bold capitalize">
                {{ company.city }}
              </div>
            </div>

            <div class="flex flex-col">
              <div
                class="mb-1 text-xs font-bold uppercase text-stone-400"
              >
                About
              </div>
              <div class="font-bold capitalize">
                {{ company.about }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="max-w-4xl">
        <div class="mt-10">
          <div class="" v-if="companyEmployeesCount > 0">
            <div class="sm:flex sm:items-center">
              <div class="sm:flex-auto">
                <h1 class="text-xl font-semibold text-gray-900">
                  Employees ({{ companyEmployeesCount }})
                </h1>
                <p class="mt-2 text-sm text-gray-700">
                  A table of employees we have on our records, for
                  this company
                </p>
              </div>
            </div>
            <div class="flex flex-col mt-8">
              <div
                class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"
              >
                <div
                  class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8"
                >
                  <div
                    class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg"
                  >
                    <table
                      class="min-w-full divide-y divide-gray-300"
                    >
                      <thead class="bg-stone-50">
                        <tr>
                          <th
                            scope="col"
                            class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"
                          >
                            Full Name
                          </th>
                          <th
                            scope="col"
                            class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            Identification Number
                          </th>
                          <th
                            scope="col"
                            class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            Date of Birth
                          </th>
                          <th
                            scope="col"
                            class="whitespace-nowrap px-2 py-3.5 text-left text-sm font-semibold text-gray-900"
                          >
                            Phone Number
                          </th>
                          <th
                            scope="col"
                            class="relative whitespace-nowrap py-3.5 pl-3 pr-4 sm:pr-6"
                          >
                            <span class="sr-only">Show</span>
                          </th>
                        </tr>
                      </thead>
                      <tbody
                        class="bg-white divide-y divide-gray-200"
                      >
                        <tr
                          v-for="employees in companyEmployees"
                          :key="employees.id"
                        >
                          <td
                            class="py-2 pl-4 pr-3 text-sm text-gray-500 capitalize whitespace-nowrap sm:pl-6"
                          >
                            {{ employees.fullName }}
                          </td>
                          <td
                            class="px-2 py-2 text-sm font-medium text-gray-900 whitespace-nowrap"
                          >
                            {{ employees.identificationNumber }}
                          </td>
                          <td
                            class="px-2 py-2 text-sm text-gray-900 whitespace-nowrap"
                          >
                            {{ employees.dob }}
                          </td>
                          <td
                            class="px-2 py-2 text-sm text-gray-500 whitespace-nowrap"
                          >
                            {{ employees.phoneNumber }}
                          </td>

                          <td
                            class="relative py-2 pl-3 pr-4 text-sm font-medium text-right whitespace-nowrap sm:pr-6"
                          >
                            <router-link
                              class="flex items-center font-bold text-teal-700 hover:text-teal-900"
                              :to="{
                                name: 'show_patient',
                                params: { id: employees.id },
                              }"
                            >
                              Show<span class="sr-only"
                                >, {{ employees.id }}</span
                              >
                              <ArrowSmallRightIcon
                                class="w-4 h-4 ml-1"
                              />
                            </router-link>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="" v-else>
            <div class="sm:flex sm:items-center">
              <div class="sm:flex-auto">
                <h1 class="text-xl font-semibold text-gray-900">
                  Employees ({{ companyEmployeesCount }})
                </h1>
                <p class="mt-2 text-sm text-gray-700">
                  A table of employees we have on our records, for
                  this company
                </p>
              </div>
            </div>
            <div class="flex flex-col mt-8">
              <div
                class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8"
              >
                <div
                  class="inline-block min-w-full py-2 align-middle md:px-6 lg:px-8"
                >
                  <div
                    class="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg"
                  >
                    <table
                      class="min-w-full divide-y divide-gray-300"
                    >
                      <thead class="bg-stone-50">
                        <tr>
                          <th
                            scope="col"
                            class="whitespace-nowrap py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-6"
                          ></th>
                        </tr>
                      </thead>
                      <tbody
                        class="flex items-center justify-center p-4 bg-white divide-y divide-gray-200"
                      >
                        <tr class="">
                          <EmptyPatientState
                            class="flex justify-center p-4"
                          />
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </q-page>
</template>

<script setup>
import { useRoute } from "vue-router";
import { useQuery } from "@vue/apollo-composable";
import gql from "graphql-tag";
import { computed } from "vue";
import {
  ArrowSmallLeftIcon,
  ArrowSmallRightIcon,
} from "@heroicons/vue/24/outline";
import EmptyPatientState from "src/components/EmptyPatientState.vue";
const route = useRoute();

const { result } = useQuery(
  gql`
    query getCompany($id: ID!) {
      company(id: $id) {
        id
        name
        suburb
        email
        industrySector
        about
        city
        phoneNumber
        streetAddress
        employees {
          id
          dob
          identificationNumber
          fullName
          phoneNumber
        }
      }
    }
  `,
  {
    id: route.params.id,
  },
  {
    fetchPolicy: "cache-and-network",
  },
);

const company = computed(() => {
  return result.value?.company ?? "Company";
});

const companyEmployees = computed(() => {
  return company.value?.employees;
});

const companyEmployeesCount = computed(() => {
  return companyEmployees.value?.length;
});
</script>
