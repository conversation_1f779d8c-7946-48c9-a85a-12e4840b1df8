<template>
  <q-page padding>
    <form @submit.prevent="submitForm">
      <PageHeading>
        <template #navigation>
          <div class="flex items-center text-gray-500 text-uppercase">
            <router-link class="flex" to="/companies">
              <ArrowSmallLeftIcon class="w-5 h-5" />
              Back
            </router-link>
          </div>
        </template>
        <template #heading>
          <div class="capitalize">new employment</div>
        </template>
        <template #description>
          Capture the detail of a new employment
        </template>
        <template #buttons>
          <q-btn
            flat
            class="text-white bg-teal-700 text-bold"
            padding="md"
            no-caps
            :loading="sendFormLoading"
            type="submit"
          >
            <template v-slot:loading>
              <q-spinner-ios />
            </template>
            <CheckIcon class="w-5 h-5 mr-1 font-bold" />
            <div class="text-sm">Save</div>
          </q-btn>
        </template>
      </PageHeading>

      <div class="max-w-2xl">
        <div
          class="grid grid-cols-1 mt-6 gap-x-4 gap-y-6 sm:grid-cols-6"
        >
          <div class="sm:col-span-4">
            <h3 class="text-lg font-semibold leading-6 text-gray-900">
              Company Details
            </h3>
          </div>

          <div class="sm:col-span-3">
            <div v-if="error">Error</div>

            <Combobox
              as="div"
              v-model="selectedCompany"
              v-if="result && result.companies"
            >
              <ComboboxLabel
                class="block text-sm font-medium text-gray-700"
                >Company*</ComboboxLabel
              >
              <div class="relative mt-1">
                <ComboboxInput
                  class="w-full py-2 pl-3 pr-10 bg-white border rounded-sm shadow-sm border-stone-300 focus:border-stone-300 focus:outline-none focus:ring-1 focus:ring-stone-500 sm:text-sm"
                  @change="query = $event.target.value"
                  :display-value="(company) => company.name"
                />
                <ComboboxButton
                  class="absolute inset-y-0 right-0 flex items-center px-2 rounded-r-md focus:outline-none"
                >
                  <ChevronUpDownIcon
                    class="w-5 h-5 text-gray-400"
                    aria-hidden="true"
                  />
                </ComboboxButton>

                <ComboboxOptions
                  v-if="filteredCompanies.length > 0"
                  class="absolute z-10 w-full py-1 mt-1 overflow-auto text-base bg-white rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
                >
                  <ComboboxOption
                    v-for="company in filteredCompanies"
                    :key="company.id"
                    :value="company"
                    as="template"
                    v-slot="{ active, selected }"
                  >
                    <li
                      :class="[
                        'relative cursor-default select-none py-2 pl-3 pr-9',
                        active
                          ? 'bg-stone-600 text-white'
                          : 'text-gray-900',
                      ]"
                    >
                      <span
                        :class="[
                          'block truncate',
                          selected && 'font-semibold',
                        ]"
                      >
                        {{ company.name }}
                      </span>

                      <span
                        v-if="selected"
                        :class="[
                          'absolute inset-y-0 right-0 flex items-center pr-4',
                          active ? 'text-white' : 'text-stone-600',
                        ]"
                      >
                        <CheckIcon
                          class="w-5 h-5"
                          aria-hidden="true"
                        />
                      </span>
                    </li>
                  </ComboboxOption>
                </ComboboxOptions>
              </div>
            </Combobox>
            <div class="mt-3 text-xs text-stone-500">
              Can't find company you looking for, add it
              <router-link class="text-teal-600" to="/companies/new"
                >here.</router-link
              >
            </div>
          </div>
          <div class="sm:col-span-4">
            <Listbox as="div" v-model="selectedMedicalType">
              <ListboxLabel
                class="block text-sm font-medium text-stone-500"
              >
                Employment Type*
              </ListboxLabel>
              <div class="relative mt-1">
                <ListboxButton
                  class="relative w-full py-2 pl-3 pr-10 text-left bg-white border border-gray-300 rounded-sm shadow-sm cursor-default focus:outline-none focus:ring-1 focus:ring-stone-500 focus:border-stone-300 sm:text-sm"
                >
                  <span class="block truncate">{{
                    selectedMedicalType.name
                  }}</span>
                  <span
                    class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"
                  >
                    <SelectorIcon
                      class="w-5 h-5 text-gray-400"
                      aria-hidden="true"
                    />
                  </span>
                </ListboxButton>

                <transition
                  leave-active-class="transition duration-100 ease-in"
                  leave-from-class="opacity-100"
                  leave-to-class="opacity-0"
                >
                  <ListboxOptions
                    class="absolute z-10 w-full py-1 mt-1 overflow-auto text-base bg-white rounded-md shadow-lg max-h-60 ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm"
                  >
                    <ListboxOption
                      as="template"
                      v-for="type in evaluationTypes"
                      :key="type.name"
                      :value="type"
                      v-slot="{ active, selected }"
                    >
                      <li
                        :class="[
                          active
                            ? 'text-white bg-stone-600'
                            : 'text-gray-900',
                          'cursor-default select-none relative py-2 pl-3 pr-9',
                        ]"
                      >
                        <span
                          :class="[
                            selected
                              ? 'font-semibold'
                              : 'font-normal',
                            'block truncate',
                          ]"
                        >
                          {{ type.name }}
                        </span>

                        <span
                          v-if="selected"
                          :class="[
                            active ? 'text-white' : 'text-stone-600',
                            'absolute inset-y-0 right-0 flex items-center pr-4',
                          ]"
                        >
                          <CheckIcon
                            class="w-5 h-5"
                            aria-hidden="true"
                          />
                        </span>
                      </li>
                    </ListboxOption>
                  </ListboxOptions>
                </transition>
              </div>
            </Listbox>
          </div>

          <div class="sm:col-span-3">
            <label
              for="department"
              class="block text-sm font-medium text-stone-400"
            >
              Department*</label
            >
            <div class="mt-1">
              <input
                autocomplete="off"
                v-model="formData.department"
                type="text"
                name="department"
                id="department"
                class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
                placeholder=""
              />
            </div>
          </div>

          <div class="sm:col-span-3">
            <label
              for="position"
              class="block text-sm font-medium text-stone-400"
            >
              Position*</label
            >
            <div class="mt-1">
              <input
                autocomplete="off"
                type="text"
                name="position"
                v-model="formData.position"
                id="position"
                class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
                placeholder=""
              />
            </div>
          </div>

          <div class="sm:col-span-4">
            <h3 class="text-lg font-semibold leading-6 text-gray-900">
              Induction
            </h3>
          </div>

          <div class="sm:col-span-3">
            <v-date-picker
              color="teal"
              v-model="formData.inductionDate"
              :masks="masks"
              class=""
            >
              <template v-slot="{ inputValue, inputEvents }">
                <label
                  for="employment_start_date"
                  class="block mb-1 text-sm font-medium text-stone-400"
                  >Start of Employment*</label
                >
                <input
                  class="w-full px-2 py-2 font-semibold border rounded-sm shadow-sm border-stone-300 focus:border-stone-300 focus:outline-none focus:ring-1 focus:ring-stone-500"
                  :value="inputValue"
                  v-on="inputEvents"
                />
              </template>
            </v-date-picker>
          </div>
          <div class="sm:col-span-4">
            <h3 class="text-lg font-semibold leading-6 text-gray-900">
              Termination
            </h3>
          </div>

          <div class="sm:col-span-3">
            <v-date-picker color="teal" :masks="masks" class="">
              <template v-slot="{ inputValue, inputEvents }">
                <label
                  for="employment_start_date"
                  class="block mb-1 text-sm font-medium text-stone-400"
                  >End of Employment</label
                >
                <input
                  class="w-full px-2 py-2 font-semibold border rounded-sm shadow-sm border-stone-300 focus:border-stone-300 focus:outline-none focus:ring-1 focus:ring-stone-500"
                  :value="inputValue"
                  v-on="inputEvents"
                />
              </template>
            </v-date-picker>
          </div>

          <div class="sm:col-span-4">
            <label
              for="termination_reason"
              class="block text-sm font-medium text-stone-400"
            >
              Reason for termination</label
            >
            <div class="mt-1">
              <input
                autocomplete="off"
                type="text"
                name="termination_reason"
                id="termination_reason"
                class="block w-full font-semibold rounded-sm shadow-sm focus:ring-stone-500 focus:border-stone-300 sm:text-sm border-stone-300 text-stone-800"
                placeholder=""
              />
            </div>
          </div>
        </div>
      </div>
    </form>
  </q-page>
</template>

<script setup>
import { reactive, ref, watch, computed } from "vue";
import gql from "graphql-tag";
import { useQuery } from "@vue/apollo-composable";
import { useMutation } from "@vue/apollo-composable";
import { useRoute, useRouter } from "vue-router";
import useVuelidate from "@vuelidate/core";
import { required, helpers } from "@vuelidate/validators";

import {
  ArrowSmallLeftIcon,
  CheckIcon,
  ChevronUpDownIcon,
} from "@heroicons/vue/24/outline";

const router = useRouter();
const route = useRoute();

const masks = {
  input: "YYYY/MM/DD",
  mode: "date",
  data: ["L", "YYYY-MM-DD", "YYYY/MM/DD"],
};

const evaluationTypes = [
  { name: "Pre-Employment" },
  { name: "Bi-Annual" },
  { name: "Annual" },
  { name: "Exit" },
];

const COMPANIES = gql`
  query listCompanies($id: ID!) {
    companies(organisationId: $id) {
      id
      name
    }
  }
`;

const CREATE_EMPLOYMENT = gql`
  mutation ($input: EmploymentInput!) {
    createEmployment(input: $input) {
      success
      employment {
        id
        patient {
          fullName
          id
        }
      }
    }
  }
`;

const {
  mutate: sendForm,
  onDone,
  loading: sendFormLoading,
} = useMutation(CREATE_EMPLOYMENT, () => ({
  variables: {
    input: {
      ...formData,
    },
  },
}));

const formData = reactive({
  companyId: null,
  patientId: parseInt(route.params.patient_id),
  inductionDate: "",
  department: "",
  position: "",
  employmentType: evaluationTypes[0].name,
});

const selectedMedicalType = ref(evaluationTypes[0]);

const rules = {
  patientId: {
    required: helpers.withMessage("Patient is required.", required),
  },
  companyId: {
    required: helpers.withMessage("Company is required.", required),
  },
  position: {
    required: helpers.withMessage(
      "Position in this employment function is required.",
      required,
    ),
  },
  department: {
    required: helpers.withMessage(
      "Department of this employment is required.",
      required,
    ),
  },
  inductionDate: {
    required: helpers.withMessage(
      "Start of employment date is required.",
      required,
    ),
  },
};

const v$ = useVuelidate(rules, formData);

const submitForm = async () => {
  const isFormCorrect = await v$.value.$validate();
  if (!isFormCorrect) return;
  sendForm();
};

onDone((result) => {
  if (result.data.createEmployment.success == true) {
    router.push(`/companies`);
  } else {
    console.log("From Error:", result.data?.createEmployment.success);
  }
});
const selectedCompany = ref({});
const query = ref("");

const { result, error } = useQuery(
  COMPANIES,
  { id: 1 },
  { fetchPolicy: "cache-and-network" },
);

watch(
  () => selectedCompany.value?.id,
  (id) => {
    formData.companyId = parseInt(id);
  },
);

watch(
  () => selectedMedicalType.value?.name,
  (name) => {
    console.log(name);
    formData.employmentType = name;
  },
);

const data = computed(() => result.value?.companies ?? []);

const filteredCompanies = computed(() =>
  query.value === ""
    ? data
    : data.value.filter((company) => {
        return company.name
          .toLowerCase()
          .includes(query.value.toLowerCase());
      }),
);
</script>
