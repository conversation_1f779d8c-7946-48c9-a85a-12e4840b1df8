import { boot } from 'quasar/wrappers'
import { DefaultApolloClient } from '@vue/apollo-composable'
import { RetryLink } from '@apollo/client/link/retry'
import { onError } from '@apollo/client/link/error'
import QueueLink from 'apollo-link-queue'
import SerializingLink from 'apollo-link-serialize'
import { useNetwork } from '@vueuse/core'
import { setContext } from '@apollo/client/link/context'
import { watch, reactive } from 'vue'
import { useOfflineMode } from 'src/composables/use-offline-mode'
import localforage from 'localforage'
import { ApolloLink } from '@apollo/client/core'
import { createUploadLink } from 'apollo-upload-client'
import {
  persistCache,
  LocalForageWrapper,
} from 'apollo3-cache-persist'
import {
  ApolloClient,
  createHttpLink,
  InMemoryCache,
  from,
} from '@apollo/client/core'
import { Cookies } from 'quasar'

export default boot(async ({ app }) => {
  // HTTP connection to the API
  const httpLink = createUploadLink({
    // You should use an absolute URL here
    uri: import.meta.env.VITE_GRAPHQL_ENDPOINT,
  })

  const errorLink = onError(({ graphQLErrors, networkError }) => {
    if (graphQLErrors)
      graphQLErrors.forEach(({ message, locations, path }) =>
        console.log(
          `[GraphQL error]: Message: ${message}, Location: ${locations}, Path: ${path}`,
        ),
      )
    if (networkError) console.log(`[Network error]: ${networkError}`)
  })

  const authLink = new ApolloLink((operation, forward) => {

    const token = Cookies.get('occusolve-token') ?? null

    operation.setContext({
      headers: {
        authorization: token ? `Token ${token}` : "",
      }
    })

    return forward(operation)
  })

  const _authLink = setContext(async (_, { headers }) => {
    // get the authentication token from local storage if it exists
    const token = Cookies.get('occusolve-token') ?? null
    // Return the headers to the context so httpLink can read them
    return {
      headers: {
        ...headers,
        authorization: `Token ${token}`,
      },
    }
  })

  const retryLink = new RetryLink()
  const queueLink = new QueueLink()
  const serializeLink = new SerializingLink()

  let network = reactive(useNetwork())

  watch(
    () => network.isOnline,
    () => {
      if (network.isOnline == true) {
        console.log('Online')
        queueLink.open()
        console.log(queueLink)
      }
      if (network.isOnline == false) {
        console.log('Offline')
        queueLink.close()
        console.log(queueLink)
      }
    },
  )

  const cache = new InMemoryCache()

  // Persistence with verification and error handling
  try {
    console.log('[Apollo Cache] Starting cache persistence...')

    await persistCache({
      cache,
      storage: new LocalForageWrapper(localforage),
      debug: process.env.DEV,
      maxSize: false, // Disable size limit for medical data
    })

    // Verify cache was restored
    const cacheData = cache.extract()
    const cacheSize = Object.keys(cacheData).length

    if (cacheSize > 0) {
      console.log(`[Apollo Cache] Successfully restored ${cacheSize} cached entities`)

      // Log sample of cached data in dev mode
      if (process.env.DEV) {
        const samples = Object.keys(cacheData).slice(0, 5)
        console.log('[Apollo Cache] Sample cached keys:', samples)
      }
    } else {
      console.log('[Apollo Cache] No cached data found, starting fresh')
    }

    // Verify LocalForage is working
    const testKey = 'apollo-cache-test'
    await localforage.setItem(testKey, { test: true })
    const testValue = await localforage.getItem(testKey)

    if (testValue?.test === true) {
      console.log('[Apollo Cache] LocalForage persistence verified')
      await localforage.removeItem(testKey)
    } else {
      console.warn('[Apollo Cache] LocalForage persistence test failed')
    }

  } catch (error) {
    console.error('[Apollo Cache] Persistence failed, using memory-only cache:', error)

    // Clear potentially corrupted cache
    try {
      await localforage.removeItem('apollo-cache-persist')
      console.log('[Apollo Cache] Cleared corrupted cache data')
    } catch (clearError) {
      console.error('[Apollo Cache] Failed to clear cache:', clearError)
    }
  }

  // Offline mode link for development testing
  const { handleApolloRequest } = useOfflineMode()
  const offlineLink = new ApolloLink((operation, forward) => {
    return handleApolloRequest(operation, forward)
  })

  // Create the apollo client
  const apolloClient = new ApolloClient({
    link: from([
      offlineLink, // Add offline simulation first
      authLink,
      errorLink,
      queueLink,
      serializeLink,
      retryLink,
      httpLink,
    ]),
    cache,
  })

  app.provide(DefaultApolloClient, apolloClient)

  // Development cache monitoring and debugging
  if (process.env.DEV) {
    // Expose cache for debugging
    window.__APOLLO_CACHE__ = cache
    window.__APOLLO_CLIENT__ = apolloClient

    // Add cache statistics function
    window.__APOLLO_CACHE_STATS__ = async () => {
      const { getCacheStats } = await import('src/utils/apollo-cache-debug')
      return getCacheStats(cache)
    }

    // Add cache clear function
    window.__APOLLO_CACHE_CLEAR__ = async () => {
      const { clearCache } = await import('src/utils/apollo-cache-debug')
      return clearCache(cache)
    }

    // Log cache operations
    const originalWrite = cache.write
    cache.write = function (...args) {
      console.log('[Cache Write]', args[0]?.result?.__typename || 'Unknown')
      return originalWrite.apply(this, args)
    }

    // Monitor cache size periodically
    setInterval(async () => {
      const stats = await window.__APOLLO_CACHE_STATS__()
      if (stats.entityCount > 1000) {
        console.warn(`[Cache] Large cache detected: ${stats.entityCount} entities, ${stats.cacheSizeKB}KB`)
      }
    }, 60000) // Check every minute
  }
})
