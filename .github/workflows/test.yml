name: Test Suite

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main, develop]

jobs:
  unit-tests:
    runs-on: ubuntu-latest

    strategy:
      matrix:
        node-version: [18.x, 20.x]

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Run linting
        run: npm run lint

      - name: Run unit tests
        run: npm run test:unit

  e2e-tests:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18.x"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Run E2E tests
        run: npm run test:e2e:ci

      - name: Upload E2E test artifacts
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-screenshots
          path: test/cypress/screenshots
          if-no-files-found: ignore

      - name: Upload E2E test videos
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: cypress-videos
          path: test/cypress/videos
          if-no-files-found: ignore

  security-audit:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: "18.x"
          cache: "npm"

      - name: Install dependencies
        run: npm ci

      - name: Run security audit
        run: npm audit --omit=dev --audit-level=moderate

  test-summary:
    runs-on: ubuntu-latest
    needs: [unit-tests, e2e-tests, security-audit]
    if: always()

    steps:
      - name: Test Summary
        run: |
          echo "## Test Results Summary" >> $GITHUB_STEP_SUMMARY
          echo "- Unit Tests: ${{ needs.unit-tests.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- E2E Tests: ${{ needs.e2e-tests.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- Security Audit: ${{ needs.security-audit.result }}" >> $GITHUB_STEP_SUMMARY

      - name: Fail if any test failed
        if: needs.unit-tests.result == 'failure' || needs.e2e-tests.result == 'failure' || needs.security-audit.result == 'failure'
        run: exit 1
