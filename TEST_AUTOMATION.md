# OccuSolve Test Automation Guide

## Overview

This document provides comprehensive guidance for running, maintaining, and extending the OccuSolve test automation suite. The test suite is designed to provide regression protection during migrations and ensure code quality throughout development.

## Test Suite Architecture

### 1. Unit Tests (Jest + Vue Test Utils)
- **Location**: `test/jest/__tests__/`
- **Purpose**: Test individual components and functions in isolation
- **Coverage**: 17%+ statement coverage achieved
- **Run Command**: `npm run test:unit`

### 2. Integration Tests (Jest + Apollo Mocking)
- **Location**: `test/jest/__tests__/`
- **Purpose**: Test component interactions with GraphQL and services
- **Coverage**: GraphQL operations, form workflows, authentication
- **Run Command**: `npm run test:unit` (included with unit tests)

### 3. Component Tests (Cypress)
- **Location**: `test/cypress/component/`
- **Purpose**: Test component behavior in browser environment
- **Coverage**: User interactions, visual testing
- **Run Command**: `npm run test:component`

### 4. End-to-End Tests (Cypress)
- **Location**: `test/cypress/integration/`
- **Purpose**: Test complete user workflows
- **Coverage**: Authentication, patient management, medical assessments
- **Run Command**: `npm run test:e2e`

## Quick Start

### Running All Tests
```bash
# Run comprehensive test suite
./scripts/test-all.sh

# Run specific test types
npm run test:unit                # Unit tests only
npm run test:unit:coverage       # Unit tests with coverage
npm run test:e2e                 # E2E tests (development)
npm run test:e2e:ci              # E2E tests (CI mode)
npm run test:component           # Component tests
```

### Pre-Migration Testing
```bash
# 1. Establish baseline
npm run test:unit:coverage
./scripts/test-all.sh

# 2. Save coverage report
cp -r test/jest/coverage reports/pre-migration-coverage

# 3. Document current test status
echo "Pre-migration test status: $(date)" > reports/migration-log.md
```

### Post-Migration Testing
```bash
# 1. Run full test suite
./scripts/test-all.sh

# 2. Compare coverage
npm run test:unit:coverage

# 3. Verify no regressions
diff -r reports/pre-migration-coverage test/jest/coverage
```

## Test Environment Configuration

### Environment Variables
```bash
# Test environment
NODE_ENV=test

# Coverage thresholds
COVERAGE_THRESHOLD_STATEMENTS=70
COVERAGE_THRESHOLD_BRANCHES=70
COVERAGE_THRESHOLD_FUNCTIONS=70
COVERAGE_THRESHOLD_LINES=70

# E2E testing
CYPRESS_BASE_URL=http://localhost:9000
CYPRESS_API_URL=http://localhost:3001/graphql

# Test database (if applicable)
USE_TEST_DB=true
TEST_DB_URL=postgresql://test:test@localhost:5432/occusolve_test
```

### Environment-Specific Configuration
The test suite supports multiple environments:

- **Development**: Full testing with coverage, non-headless E2E
- **Testing**: Stricter thresholds, headless E2E
- **Staging**: Production-like testing with real APIs
- **Production**: Smoke tests only

Configuration is managed in `test/config/test-environments.js`.

## CI/CD Integration

### GitHub Actions Workflow
The test suite is integrated with GitHub Actions (`.github/workflows/test.yml`):

1. **Unit Tests**: Run on Node.js 18.x and 20.x
2. **E2E Tests**: Run on Ubuntu with application build
3. **Component Tests**: Browser-based component testing
4. **Security Audit**: Dependency vulnerability scanning
5. **Coverage Reporting**: Automatic coverage comments on PRs

### Pre-commit Hooks
Pre-commit hooks are configured in `.pre-commit-config.yaml`:

- Code formatting and linting
- Unit test execution
- Coverage threshold checking
- Commit message validation

To install pre-commit hooks:
```bash
pip install pre-commit
pre-commit install
```

## Test Data Management

### Mock Data
Test fixtures are located in `test/jest/fixtures/`:
- `patients.js`: Patient mock data and factories
- `companies.js`: Company and clinic mock data
- `medicals.js`: Medical assessment mock data
- `assessments.js`: Various assessment type mock data

### Creating Test Data
```javascript
import { createMockPatient } from 'test/jest/fixtures/patients'

// Create patient with default data
const patient = createMockPatient()

// Create patient with custom data
const customPatient = createMockPatient({
  firstName: 'John',
  lastName: 'Doe',
  email: '<EMAIL>'
})
```

### GraphQL Mocking
```javascript
import { mountWithApollo, createMockResponse } from 'test/jest/helpers/apollo-mock'

const mocks = [
  createMockResponse(GET_PATIENTS_QUERY, { id: '1' }, { patients: mockPatients })
]

const wrapper = mountWithApollo(PatientIndexPage, {}, mocks)
```

## Coverage Requirements

### Current Coverage Targets
- **Statements**: 70%
- **Branches**: 70%
- **Functions**: 70%
- **Lines**: 70%

### Coverage Reports
Coverage reports are generated in multiple formats:
- **HTML**: `test/jest/coverage/lcov-report/index.html`
- **LCOV**: `test/jest/coverage/lcov.info`
- **JSON**: `test/jest/coverage/coverage-final.json`

### Improving Coverage
1. Identify uncovered code: `npm run test:unit:coverage`
2. Add tests for uncovered functions/branches
3. Focus on business logic and critical paths
4. Use coverage reports to guide test development

## Debugging Tests

### Unit Test Debugging
```bash
# Run specific test file
npm run test:unit -- PatientForm.spec.js

# Run tests in watch mode
npm run test:unit:watch

# Run with verbose output
npm run test:unit -- --verbose

# Debug specific test
npm run test:unit -- --testNamePattern="validates required fields"
```

### E2E Test Debugging
```bash
# Run E2E tests in headed mode
npm run test:e2e

# Run specific E2E test
npx cypress run --spec "test/cypress/integration/auth/login.spec.js"

# Open Cypress Test Runner
npx cypress open
```

### Common Issues and Solutions

#### Jest Issues
- **Import errors**: Check module name mapping in `jest.config.js`
- **Apollo mocking**: Ensure GraphQL queries match exactly
- **Component mounting**: Verify all required props and dependencies

#### Cypress Issues
- **Element not found**: Use proper selectors and wait commands
- **Timing issues**: Add appropriate waits and assertions
- **Authentication**: Use custom login command

## Extending the Test Suite

### Adding New Unit Tests
1. Create test file in appropriate directory under `test/jest/__tests__/`
2. Follow naming convention: `ComponentName.spec.js`
3. Use existing test utilities and fixtures
4. Ensure proper test isolation and cleanup

### Adding New E2E Tests
1. Create test file in `test/cypress/integration/`
2. Use custom commands for common operations
3. Follow page object pattern for complex workflows
4. Include proper setup and teardown

### Creating Custom Test Utilities
1. Add utilities to `test/jest/helpers/`
2. Export functions for reuse across tests
3. Document utility functions
4. Include TypeScript definitions if applicable

## Best Practices

### Test Writing
- Write descriptive test names
- Use AAA pattern (Arrange, Act, Assert)
- Test behavior, not implementation
- Include both positive and negative test cases
- Mock external dependencies

### Test Maintenance
- Keep tests simple and focused
- Update tests when requirements change
- Remove obsolete tests
- Refactor common test patterns into utilities

### Performance
- Use `shallowMount` when full rendering isn't needed
- Mock heavy dependencies
- Parallelize test execution where possible
- Clean up resources after tests

## Migration Testing Strategy

### Before Migration
1. **Establish Baseline**: Run full test suite and document results
2. **Increase Coverage**: Add tests for critical functionality
3. **Document Dependencies**: List all external dependencies and versions
4. **Create Test Data**: Ensure comprehensive test data coverage

### During Migration
1. **Incremental Testing**: Run tests after each migration step
2. **Monitor Coverage**: Ensure coverage doesn't decrease
3. **Update Tests**: Modify tests as APIs/components change
4. **Document Changes**: Track all test modifications

### After Migration
1. **Full Validation**: Run complete test suite
2. **Performance Testing**: Verify no performance regressions
3. **Update Documentation**: Reflect any changes in test procedures
4. **Establish New Baseline**: Document post-migration test status

## Support and Troubleshooting

### Getting Help
- Review test logs and error messages
- Check existing test patterns for similar scenarios
- Consult test documentation and comments
- Use debugging tools and verbose output

### Common Commands
```bash
# Clear test cache
npm run test:unit -- --clearCache

# Update snapshots
npm run test:unit -- --updateSnapshot

# Run tests with coverage
npm run test:unit:coverage

# Generate test report
./scripts/test-all.sh
```

This comprehensive test automation suite provides robust regression protection and ensures code quality throughout the development lifecycle.
