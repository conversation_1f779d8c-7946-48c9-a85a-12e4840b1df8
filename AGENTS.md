# Repository Guidelines

## Project Structure & Module Organization
- `src/`: App code — `components/`, `pages/`, `layouts/`, `router/`, `boot/`, `composables/`, `utils/`, `assets/`, `gql/`, `mirage/`.
- `public/`: Static assets served as‑is.
- `test/`: Testing — `jest/` (unit), `cypress/` (e2e & component), `config/` (env setup).
- `scripts/`: Helper scripts for test/coverage workflows.

## Build, Test, and Development Commands
- Dev server: `npm run dev` (alias for `quasar dev`, runs on `http://localhost:9002`).
- Build: `quasar build` (see `quasar.config.js`).
- Lint: `npm run lint` (ESLint for `.js`/`.vue`).
- Format: `npm run format` (Prettier).
- Unit tests: `npm run test:unit` | watch: `:watch` | CI: `:ci` | coverage: `:coverage`.
- E2E: `npm run test:e2e` (opens Cypress after app ready) | CI: `:e2e:ci`.
- Component tests: `npm run test:component`.

## Coding Style & Naming Conventions
- Formatting: Prettier (`semi: true`, `trailingComma: all`, `printWidth: 70`).
- Linting: ESLint with `plugin:vue/vue3-essential` + Prettier.
- Vue components/pages: PascalCase filenames (e.g., `PatientForm.vue`).
- Composables: kebab-case with `use-` prefix (e.g., `use-background-loading.js`).
- Folders: lowercased; avoid spaces; prefer descriptive names.

## Testing Guidelines
- Frameworks: Jest (unit), Cypress (e2e + component).
- Locations: unit under `test/jest/__tests__/`; e2e under `test/cypress/integration/`.
- Naming: `*.spec.js` or `*.test.js`; snapshots allowed where useful.
- Coverage: thresholds in `jest.config.js` (see `coverageThreshold`). Run `npm run test:unit:coverage`.

## Commit & Pull Request Guidelines
- Commits: Conventional Commits (`feat:`, `fix:`, `chore:`, `refactor:`). Example: `feat: add CloudLoadingIndicator to MainLayout`.
- Branching: short, hyphenated names (e.g., `feat/patient-form-validation`).
- PRs: include description, linked issues (`Closes #123`), before/after screenshots for UI, and test notes. Ensure `npm run lint` and tests pass.
- CI: GitHub Actions runs lint, unit, e2e, and a security audit on PRs.

## Security & Configuration Tips
- Secrets: never commit `.env` values; use local `.env`/`.env.development`.
- Apollo/GraphQL: verify endpoints in boot files; mock with Mirage for tests.
- Large changes: update or add tests and docs (e.g., `SITEMAP.md`).
