const esModules = ["quasar", "quasar/lang", "lodash-es", "@vue", "@apollo"].join("|");

module.exports = {
  globals: {
    __DEV__: true,
    // TODO: Remove if resolved natively
    // See https://github.com/vuejs/vue-jest/issues/175
    "vue-jest": {
      pug: { doctype: "html" },
    },
  },
  // Use node environment with DOM mocks to avoid localStorage issues
  testEnvironment: "node",
  // noStackTrace: true,
  // bail: true,
  // cache: false,
  // verbose: true,
  // watch: true,
  collectCoverage: false,
  coverageDirectory: "<rootDir>/test/jest/coverage",
  collectCoverageFrom: [
    "<rootDir>/src/**/*.vue",
    "<rootDir>/src/**/*.js",
    "<rootDir>/src/**/*.jsx",
  ],
  // Needed in JS codebases too because of feature flags
  coveragePathIgnorePatterns: [
    "/node_modules/",
    ".d.ts$",
    "<rootDir>/src/boot/apollo.js", // Exclude apollo.js from coverage due to import.meta issues
    "<rootDir>/src/mirage/",
  ],
  coverageThreshold: {
    global: {
      branches: 0.2,
      functions: 0.7,
      lines: 17,
      statements: 17
    },
  },
  testMatch: [
    "<rootDir>/test/jest/__tests__/**/*.(spec|test).js",
    "<rootDir>/src/**/*.jest.(spec|test).js",
  ],
  moduleFileExtensions: ["vue", "js", "jsx", "json"],
  moduleNameMapper: {
    "^quasar$": "quasar/dist/quasar.client.js",
    "^~/(.*)$": "<rootDir>/$1",
    "^src/(.*)$": "<rootDir>/src/$1",
    "^app/(.*)$": "<rootDir>/$1",
    "^components/(.*)$": "<rootDir>/src/components/$1",
    "^layouts/(.*)$": "<rootDir>/src/layouts/$1",
    "^pages/(.*)$": "<rootDir>/src/pages/$1",
    "^assets/(.*)$": "<rootDir>/src/assets/$1",
    "^boot/(.*)$": "<rootDir>/src/boot/$1",
    "^test/(.*)$": "<rootDir>/test/$1",
    "^src/composables/use-offline-mode$": "<rootDir>/test/jest/mocks/use-offline-mode.js",
    "^localforage$": "<rootDir>/test/jest/mocks/localforage.js",
    "\\.(css|less|scss|sass)$": "identity-obj-proxy",
    "\\.(jpg|jpeg|png|gif|eot|otf|webp|svg|ttf|woff|woff2|mp4|webm|wav|mp3|m4a|aac|oga)$": "jest-transform-stub",
  },
  transform: {
    ".*\\.vue$": "@vue/vue3-jest",
    ".*\\.js$": "babel-jest",
    ".+\\.(css|styl|less|sass|scss|svg|png|jpg|ttf|woff|woff2)$":
      "jest-transform-stub",
    // use these if NPM is being flaky, care as hosting could interfere with these
    // '.*\\.vue$': '@quasar/quasar-app-extension-testing-unit-jest/node_modules/vue-jest',
    // '.*\\.js$': '@quasar/quasar-app-extension-testing-unit-jest/node_modules/babel-jest'
  },
  transformIgnorePatterns: [`node_modules/(?!(${esModules}))`],
  snapshotSerializers: ["jest-serializer-vue"],
  setupFiles: [
    "<rootDir>/test/jest/setup-globals.js"
  ],
  setupFilesAfterEnv: [
    "<rootDir>/test/jest/setup.js",
    "<rootDir>/test/config/jest-environment-setup.js"
  ],
};
