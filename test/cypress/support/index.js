// ***********************************************************
// This example support/index.js is processed and
// loaded automatically before your e2e test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

import "./commands";
import "@cypress/code-coverage/support";

// Handle uncaught exceptions from the application
Cypress.on('uncaught:exception', (err, runnable) => {
    // Returning false here prevents Cypress from failing the test
    // for expected authentication errors during testing
    if (err.message.includes('Invalid email or password')) {
        return false
    }
    if (err.message.includes('ApolloError')) {
        return false
    }
    if (err.message.includes('Cannot read properties of undefined')) {
        return false
    }
    if (err.message.includes('fetchFromNetwork')) {
        return false
    }
    // Let other errors fail the test
    return true
})
