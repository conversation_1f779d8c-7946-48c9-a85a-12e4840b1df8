// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add("login", (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add("drag", { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add("dismiss", { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This is will overwrite an existing command --
// Cypress.Commands.overwrite("visit", (originalFn, url, options) => { ... })

// Custom data-cy command for more robust element selection
Cypress.Commands.add('dataCy', (value) => {
    return cy.get(`[data-cy=${value}]`)
})

// ***********************************************
// Custom commands for OccuSolve testing
// ***********************************************

/**
 * Login command for authentication
 * @param {string} email - User email
 * @param {string} password - User password
 */
Cypress.Commands.add('login', (email, password) => {
    cy.session([email, password], () => {
        // Mock successful login response and other GraphQL queries
        cy.intercept('POST', '**/graphql', (req) => {
            if (req.body.query && req.body.query.includes('login')) {
                req.reply({
                    statusCode: 200,
                    body: {
                        data: {
                            login: {
                                token: 'mock-jwt-token',
                                id: 'test-user-id'
                            }
                        }
                    }
                })
            } else if (req.body.query && req.body.query.includes('recentPatients')) {
                req.reply({
                    statusCode: 200,
                    body: {
                        data: {
                            recentPatients: []
                        }
                    }
                })
            } else {
                // Default mock for other GraphQL queries
                req.reply({
                    statusCode: 200,
                    body: {
                        data: {}
                    }
                })
            }
        }).as('loginRequest')

        cy.visit('/login')
        cy.get('input[type="email"]').type(email)
        cy.get('input[type="password"]').type(password)
        cy.get('button[type="submit"]').click()

        // Wait for successful login
        cy.url().should('not.include', '/login')
        cy.getCookie('occusolve-token').should('exist')
    })
})

/**
 * Create a test patient
 * @param {Object} patientData - Patient information
 */
Cypress.Commands.add('createPatient', (patientData = {}) => {
    const defaultData = {
        identificationNumber: '1234567890123',
        firstName: 'Test',
        lastName: 'Patient',
        email: '<EMAIL>',
        phoneNumber: '**********',
        gender: 'Male',
        dob: '1990-01-01'
    }

    const data = { ...defaultData, ...patientData }

    cy.visit('/patients/new')
    cy.get('input[name="identificationNumber"]').type(data.identificationNumber)
    cy.get('input[name="firstName"]').type(data.firstName)
    cy.get('input[name="lastName"]').type(data.lastName)
    cy.get('input[name="email"]').type(data.email)
    cy.get('input[name="phoneNumber"]').type(data.phoneNumber)
    cy.get('select[name="gender"]').select(data.gender)
    cy.get('input[name="dob"]').type(data.dob)
    cy.get('button[type="submit"]').click()

    cy.url().should('not.include', '/new')
})

/**
 * Create a test medical assessment
 * @param {Object} medicalData - Medical assessment information
 */
Cypress.Commands.add('createMedical', (medicalData = {}) => {
    const defaultData = {
        medicalType: 'Pre-Employment',
        physicalExam: true,
        audioTest: false,
        visionTest: false
    }

    const data = { ...defaultData, ...medicalData }

    cy.visit('/medicals/new')

    // Select patient and clinic (first available options)
    cy.get('select[name="patientId"]').select(1)
    cy.get('select[name="clinicId"]').select(1)
    cy.get('select[name="medicalType"]').select(data.medicalType)

    // Select assessment types
    if (data.physicalExam) {
        cy.get('input[name="physicalExamPerformed"]').check()
    }
    if (data.audioTest) {
        cy.get('input[name="audioPerformed"]').check()
    }
    if (data.visionTest) {
        cy.get('input[name="visionScreeningPerformed"]').check()
    }

    cy.get('button[type="submit"]').click()
    cy.url().should('not.include', '/new')
})

/**
 * Wait for GraphQL request to complete
 * @param {string} operationName - GraphQL operation name
 */
Cypress.Commands.add('waitForGraphQL', (operationName) => {
    cy.intercept('POST', '**/graphql', (req) => {
        if (req.body.operationName === operationName) {
            req.alias = operationName
        }
    })
    cy.wait(`@${operationName}`)
})

/**
 * Mock GraphQL response
 * @param {string} operationName - GraphQL operation name
 * @param {Object} response - Mock response data
 */
Cypress.Commands.add('mockGraphQL', (operationName, response) => {
    cy.intercept('POST', '**/graphql', (req) => {
        if (req.body.operationName === operationName) {
            req.reply(response)
        }
    }).as(operationName)
})
