/**
 * Test Environment Configuration
 * Manages different test environments and their specific settings
 */

const environments = {
  development: {
    name: 'Development',
    baseUrl: 'http://localhost:9000',
    apiUrl: 'http://localhost:3001/graphql',
    timeout: 10000,
    retries: 1,
    coverage: {
      enabled: true,
      threshold: {
        statements: 70,
        branches: 70,
        functions: 70,
        lines: 70
      }
    },
    e2e: {
      enabled: true,
      headless: false,
      video: true,
      screenshots: true
    }
  },

  testing: {
    name: 'Testing',
    baseUrl: 'http://localhost:9000',
    apiUrl: 'http://localhost:3001/graphql',
    timeout: 15000,
    retries: 2,
    coverage: {
      enabled: true,
      threshold: {
        statements: 75,
        branches: 70,
        functions: 75,
        lines: 75
      }
    },
    e2e: {
      enabled: true,
      headless: true,
      video: true,
      screenshots: true
    }
  },

  staging: {
    name: 'Staging',
    baseUrl: 'https://staging.occusolve.com',
    apiUrl: 'https://staging-api.occusolve.com/graphql',
    timeout: 20000,
    retries: 3,
    coverage: {
      enabled: false,
      threshold: {
        statements: 80,
        branches: 75,
        functions: 80,
        lines: 80
      }
    },
    e2e: {
      enabled: true,
      headless: true,
      video: true,
      screenshots: true
    }
  },

  production: {
    name: 'Production',
    baseUrl: 'https://app.occusolve.com',
    apiUrl: 'https://api.occusolve.com/graphql',
    timeout: 30000,
    retries: 3,
    coverage: {
      enabled: false,
      threshold: {
        statements: 85,
        branches: 80,
        functions: 85,
        lines: 85
      }
    },
    e2e: {
      enabled: true,
      headless: true,
      video: false,
      screenshots: true
    }
  }
}

/**
 * Get configuration for current environment
 * @param {string} env - Environment name (defaults to NODE_ENV or 'development')
 * @returns {Object} Environment configuration
 */
function getConfig(env = process.env.NODE_ENV || 'development') {
  const config = environments[env]
  
  if (!config) {
    console.warn(`Unknown environment: ${env}, falling back to development`)
    return environments.development
  }
  
  return {
    ...config,
    environment: env,
    isProduction: env === 'production',
    isDevelopment: env === 'development',
    isTesting: env === 'testing' || env === 'test',
    isStaging: env === 'staging'
  }
}

/**
 * Get Jest configuration for current environment
 * @param {string} env - Environment name
 * @returns {Object} Jest configuration overrides
 */
function getJestConfig(env) {
  const config = getConfig(env)
  
  return {
    testTimeout: config.timeout,
    coverageThreshold: {
      global: config.coverage.threshold
    },
    collectCoverage: config.coverage.enabled,
    setupFilesAfterEnv: [
      '<rootDir>/test/jest/setup.js',
      '<rootDir>/test/config/jest-environment-setup.js'
    ]
  }
}

/**
 * Get Cypress configuration for current environment
 * @param {string} env - Environment name
 * @returns {Object} Cypress configuration overrides
 */
function getCypressConfig(env) {
  const config = getConfig(env)
  
  return {
    baseUrl: config.baseUrl,
    defaultCommandTimeout: config.timeout,
    requestTimeout: config.timeout,
    responseTimeout: config.timeout,
    retries: {
      runMode: config.retries,
      openMode: 0
    },
    video: config.e2e.video,
    screenshotOnRunFailure: config.e2e.screenshots,
    env: {
      apiUrl: config.apiUrl,
      environment: env
    }
  }
}

/**
 * Test data configuration for different environments
 */
const testData = {
  development: {
    users: {
      admin: {
        email: '<EMAIL>',
        password: 'password123'
      },
      user: {
        email: '<EMAIL>',
        password: 'password123'
      }
    },
    companies: [
      { name: 'Test Company 1', id: 'test-company-1' },
      { name: 'Test Company 2', id: 'test-company-2' }
    ]
  },
  
  testing: {
    users: {
      admin: {
        email: '<EMAIL>',
        password: 'testpass123'
      },
      user: {
        email: '<EMAIL>',
        password: 'testpass123'
      }
    },
    companies: [
      { name: 'Testing Corp', id: 'testing-corp' }
    ]
  },
  
  staging: {
    users: {
      admin: {
        email: '<EMAIL>',
        password: process.env.STAGING_ADMIN_PASSWORD
      },
      user: {
        email: '<EMAIL>',
        password: process.env.STAGING_USER_PASSWORD
      }
    }
  }
}

/**
 * Get test data for current environment
 * @param {string} env - Environment name
 * @returns {Object} Test data configuration
 */
function getTestData(env) {
  return testData[env] || testData.development
}

module.exports = {
  environments,
  getConfig,
  getJestConfig,
  getCypressConfig,
  getTestData
}
