/**
 * Jest Environment Setup
 * Additional setup for Jest tests based on environment
 */

const { getConfig, getTestData } = require('./test-environments')

// Get current environment configuration
const config = getConfig()
const testData = getTestData(config.environment)

// Set global test configuration
global.TEST_CONFIG = config
global.TEST_DATA = testData

// Environment-specific setup
if (config.isDevelopment) {
  // Development-specific test setup
  console.log('🧪 Running tests in DEVELOPMENT mode')

  // Enable verbose logging for development
  global.console = {
    ...console,
    log: jest.fn((...args) => {
      if (process.env.VERBOSE_TESTS === 'true') {
        console.log(...args)
      }
    })
  }
}

if (config.isTesting) {
  // Testing environment setup
  console.log('🧪 Running tests in TESTING mode')

  // Stricter timeouts for testing environment
  jest.setTimeout(config.timeout)
}

if (config.isStaging) {
  // Staging environment setup
  console.log('🧪 Running tests in STAGING mode')

  // Longer timeouts for staging
  jest.setTimeout(config.timeout)

  // Mock external services in staging
  jest.mock('external-service', () => ({
    sendEmail: jest.fn(),
    uploadFile: jest.fn()
  }))
}

// Global test utilities
global.waitFor = (ms) => new Promise(resolve => setTimeout(resolve, ms))

global.createTestId = (prefix = 'test') => {
  return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

// Mock localStorage for all environments
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
global.localStorage = localStorageMock

// Mock sessionStorage for all environments
const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
}
global.sessionStorage = sessionStorageMock

// Mock fetch for all environments
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
  })
)

// Environment-specific API mocking
if (config.isDevelopment || config.isTesting) {
  // Mock GraphQL endpoint for development and testing
  global.fetch.mockImplementation((url, options) => {
    if (url.includes('/graphql')) {
      // Mock GraphQL responses based on operation
      const body = JSON.parse(options.body || '{}')
      const operationName = body.operationName

      // Return appropriate mock data based on operation
      return Promise.resolve({
        ok: true,
        status: 200,
        json: () => Promise.resolve({
          data: getMockGraphQLResponse(operationName)
        })
      })
    }

    // Default fetch behavior
    return Promise.resolve({
      ok: true,
      status: 200,
      json: () => Promise.resolve({}),
    })
  })
}

/**
 * Get mock GraphQL response based on operation name
 * @param {string} operationName - GraphQL operation name
 * @returns {Object} Mock response data
 */
function getMockGraphQLResponse(operationName) {
  const mockResponses = {
    login: {
      login: {
        token: 'mock-jwt-token',
        id: 'mock-user-id'
      }
    },
    getPatients: {
      patients: [
        {
          id: '1',
          fullName: 'John Doe',
          email: '<EMAIL>',
          phoneNumber: '**********'
        }
      ]
    },
    getMedicals: {
      medicals: [
        {
          id: '1',
          name: 'Medical Assessment 1',
          status: 'draft',
          outcome: 'Pending'
        }
      ]
    }
  }

  return mockResponses[operationName] || {}
}

// Setup test database if needed
if (config.isTesting && process.env.USE_TEST_DB === 'true') {
  beforeAll(async () => {
    // Setup test database
    console.log('Setting up test database...')
    // Add database setup logic here
  })

  afterAll(async () => {
    // Cleanup test database
    console.log('Cleaning up test database...')
    // Add database cleanup logic here
  })
}

// Global test cleanup
afterEach(() => {
  // Clear all mocks after each test
  jest.clearAllMocks()

  // Reset localStorage and sessionStorage
  localStorageMock.clear()
  sessionStorageMock.clear()

  // Reset fetch mock
  if (global.fetch.mockClear) {
    global.fetch.mockClear()
  }
})

// Error handling for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason)
  // Never exit the process in test environment
  // Tests should handle their own failures
})

console.log(`✅ Jest environment setup complete for ${config.name} environment`)
