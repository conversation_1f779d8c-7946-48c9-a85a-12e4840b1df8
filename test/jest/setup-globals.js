/**
 * Jest Global Setup
 * This file runs BEFORE any modules are imported, allowing us to mock
 * global objects like localStorage that might be accessed during module loading
 */

// Mock localStorage with proper storage behavior
const storage = {}
const localStorageMock = {
  getItem: jest.fn((key) => storage[key] || null),
  setItem: jest.fn((key, value) => { storage[key] = value }),
  removeItem: jest.fn((key) => { delete storage[key] }),
  clear: jest.fn(() => { Object.keys(storage).forEach(key => delete storage[key]) }),
  length: 0,
  key: jest.fn(() => null)
}

// Mock sessionStorage
const sessionStorageMock = {
  getItem: jest.fn(() => null),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn(() => null)
}

// Set up localStorage mock
Object.defineProperty(global, 'localStorage', {
  value: localStorageMock,
  writable: true,
  configurable: true
})

// Set up sessionStorage mock
Object.defineProperty(global, 'sessionStorage', {
  value: sessionStorageMock,
  writable: true,
  configurable: true
})

// Mock window object for browser APIs
if (typeof window === 'undefined') {
  global.window = {
    localStorage: localStorageMock,
    sessionStorage: sessionStorageMock,
    dispatchEvent: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    CustomEvent: jest.fn(),
    document: {
      createElement: jest.fn(() => ({
        setAttribute: jest.fn(),
        getAttribute: jest.fn(),
        appendChild: jest.fn(),
        removeChild: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        style: {},
        classList: {
          add: jest.fn(),
          remove: jest.fn(),
          contains: jest.fn(() => false),
          toggle: jest.fn()
        }
      })),
      getElementById: jest.fn(),
      querySelector: jest.fn(),
      querySelectorAll: jest.fn(() => []),
      body: {
        appendChild: jest.fn(),
        removeChild: jest.fn()
      },
      head: {
        appendChild: jest.fn(),
        removeChild: jest.fn()
      }
    }
  }
} else {
  // Ensure window has storage APIs
  Object.defineProperty(window, 'localStorage', {
    value: localStorageMock,
    writable: true,
    configurable: true
  })

  Object.defineProperty(window, 'sessionStorage', {
    value: sessionStorageMock,
    writable: true,
    configurable: true
  })

  // Mock other window APIs that might be needed
  window.dispatchEvent = jest.fn()
  window.addEventListener = jest.fn()
  window.removeEventListener = jest.fn()
  window.CustomEvent = jest.fn()
}

// Mock document if it doesn't exist
if (typeof document === 'undefined') {
  global.document = global.window.document
}

// Mock location
Object.defineProperty(window, 'location', {
  value: {
    href: 'http://localhost:3000',
    origin: 'http://localhost:3000',
    protocol: 'http:',
    host: 'localhost:3000',
    hostname: 'localhost',
    port: '3000',
    pathname: '/',
    search: '',
    hash: ''
  },
  writable: true,
  configurable: true
})

// Mock navigator
Object.defineProperty(window, 'navigator', {
  value: {
    userAgent: 'jest',
    language: 'en-US',
    languages: ['en-US', 'en'],
    onLine: true
  },
  writable: true,
  configurable: true
})

// Mock console methods to prevent noise in tests
const originalConsole = global.console
global.console = {
  ...originalConsole,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
}

// Mock fetch
global.fetch = jest.fn(() =>
  Promise.resolve({
    ok: true,
    status: 200,
    statusText: 'OK',
    json: () => Promise.resolve({}),
    text: () => Promise.resolve(''),
    blob: () => Promise.resolve(new Blob()),
    arrayBuffer: () => Promise.resolve(new ArrayBuffer(0))
  })
)

// Mock URL and URLSearchParams
global.URL = class URL {
  constructor(url, base) {
    this.href = url
    this.origin = 'http://localhost:3000'
    this.protocol = 'http:'
    this.host = 'localhost:3000'
    this.hostname = 'localhost'
    this.port = '3000'
    this.pathname = '/'
    this.search = ''
    this.hash = ''
  }
}

global.URLSearchParams = class URLSearchParams {
  constructor() {
    this.params = new Map()
  }

  get(key) {
    return this.params.get(key)
  }

  set(key, value) {
    this.params.set(key, value)
  }

  has(key) {
    return this.params.has(key)
  }

  delete(key) {
    this.params.delete(key)
  }

  toString() {
    return Array.from(this.params.entries())
      .map(([key, value]) => `${key}=${value}`)
      .join('&')
  }
}

console.log('✅ Jest global setup complete - localStorage and other browser APIs mocked')
