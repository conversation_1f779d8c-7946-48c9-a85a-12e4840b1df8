import { mount, shallowMount } from '@vue/test-utils'
import { installQuasarPlugin } from '@quasar/quasar-app-extension-testing-unit-jest'
import { Quasar, Dialog, Notify, Loading } from 'quasar'
import { createRouter, createMemoryHistory } from 'vue-router'
import routes from 'src/router/routes'

// Install Quasar for individual tests if needed
export function setupQuasar() {
  installQuasarPlugin({
    plugins: {
      Dialog,
      Notify,
      Loading
    }
  })
}

/**
 * Creates a test router instance
 * @param {String} initialRoute - Initial route path
 * @returns {Object} Router instance
 */
export function createTestRouter(initialRoute = '/') {
  const router = createRouter({
    history: createMemoryHistory(),
    routes
  })

  router.push(initialRoute)
  return router
}

/**
 * Mounts a component with common test setup
 * @param {Object} component - Vue component
 * @param {Object} options - Mount options
 * @returns {Object} Wrapper instance
 */
export function mountComponent(component, options = {}) {
  const router = options.router || createTestRouter()

  return mount(component, {
    global: {
      plugins: [router],
      mocks: {
        $route: router.currentRoute.value,
        $router: router,
        ...options.mocks
      },
      stubs: {
        'router-link': true,
        'router-view': true,
        ...options.stubs
      },
      ...options.global
    },
    ...options
  })
}

/**
 * Shallow mounts a component with common test setup
 * @param {Object} component - Vue component
 * @param {Object} options - Mount options
 * @returns {Object} Wrapper instance
 */
export function shallowMountComponent(component, options = {}) {
  const router = options.router || createTestRouter()

  return shallowMount(component, {
    global: {
      plugins: [router],
      mocks: {
        $route: router.currentRoute.value,
        $router: router,
        ...options.mocks
      },
      stubs: {
        'router-link': true,
        'router-view': true,
        ...options.stubs
      },
      ...options.global
    },
    ...options
  })
}

/**
 * Triggers an event and waits for Vue to update
 * @param {Object} wrapper - Vue test utils wrapper
 * @param {String} event - Event name
 * @param {*} payload - Event payload
 */
export async function triggerAndWait(wrapper, event, payload) {
  await wrapper.trigger(event, payload)
  await wrapper.vm.$nextTick()
}

/**
 * Sets input value and triggers input event
 * @param {Object} input - Input wrapper
 * @param {String} value - Value to set
 */
export async function setInputValue(input, value) {
  await input.setValue(value)
  await input.trigger('input')
  await input.trigger('blur')
}

/**
 * Waits for a condition to be true
 * @param {Function} condition - Condition function
 * @param {Number} timeout - Timeout in ms
 * @param {Number} interval - Check interval in ms
 */
export function waitFor(condition, timeout = 5000, interval = 50) {
  return new Promise((resolve, reject) => {
    const startTime = Date.now()

    const check = () => {
      if (condition()) {
        resolve()
      } else if (Date.now() - startTime >= timeout) {
        reject(new Error('Timeout waiting for condition'))
      } else {
        setTimeout(check, interval)
      }
    }

    check()
  })
}

/**
 * Creates a mock Quasar $q object
 * @returns {Object} Mock $q object
 */
export function createMockQuasar() {
  return {
    cookies: {
      get: jest.fn(),
      set: jest.fn(),
      remove: jest.fn()
    },
    notify: jest.fn(),
    dialog: jest.fn(),
    loading: {
      show: jest.fn(),
      hide: jest.fn()
    }
  }
}

/**
 * Flushes all pending promises
 */
export function flushPromises() {
  return new Promise(resolve => setImmediate(resolve))
}
