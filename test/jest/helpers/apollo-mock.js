import { InMemoryCache, ApolloClient } from '@apollo/client/core'
import { mount, shallowMount } from '@vue/test-utils'
import { DefaultApolloClient } from '@vue/apollo-composable'

/**
 * Creates a mocked Apollo client for testing
 * @param {Array} mocks - Array of GraphQL mocks
 * @param {Object} cache - Optional cache instance
 * @returns {Object} Apollo client instance
 */
export function createMockApolloClient(mocks = [], cache = null) {
  // Create a simple mock link that returns the mocked responses
  const mockLink = {
    request: (operation) => {
      const mock = mocks.find(m =>
        m.request.query === operation.query &&
        JSON.stringify(m.request.variables || {}) === JSON.stringify(operation.variables || {})
      )

      if (mock) {
        if (mock.error) {
          return Promise.reject(mock.error)
        }
        return Promise.resolve(mock.result)
      }

      return Promise.reject(new Error(`No mock found for operation: ${operation.operationName}`))
    }
  }

  return new ApolloClient({
    link: mockLink,
    cache: cache || new InMemoryCache(),
    defaultOptions: {
      watchQuery: { errorPolicy: 'all' },
      query: { errorPolicy: 'all' }
    }
  })
}

/**
 * Mounts a Vue component with Apollo mocking
 * @param {Object} component - Vue component to mount
 * @param {Object} options - Mount options
 * @param {Array} mocks - GraphQL mocks
 * @returns {Object} Wrapper instance
 */
export function mountWithApollo(component, options = {}, mocks = []) {
  const apolloClient = createMockApolloClient(mocks)

  return mount(component, {
    global: {
      provide: {
        [DefaultApolloClient]: apolloClient
      },
      ...options.global
    },
    ...options
  })
}

/**
 * Shallow mounts a Vue component with Apollo mocking
 * @param {Object} component - Vue component to mount
 * @param {Object} options - Mount options
 * @param {Array} mocks - GraphQL mocks
 * @returns {Object} Wrapper instance
 */
export function shallowMountWithApollo(component, options = {}, mocks = []) {
  const apolloClient = createMockApolloClient(mocks)

  return shallowMount(component, {
    global: {
      provide: {
        [DefaultApolloClient]: apolloClient
      },
      ...options.global
    },
    ...options
  })
}

/**
 * Creates a mock GraphQL response
 * @param {Object} query - GraphQL query
 * @param {Object} variables - Query variables
 * @param {Object} result - Expected result
 * @param {Object} error - Optional error
 * @returns {Object} Mock object
 */
export function createMockResponse(query, variables = {}, result = {}, error = null) {
  const mock = {
    request: {
      query,
      variables
    }
  }

  if (error) {
    mock.error = error
  } else {
    mock.result = { data: result }
  }

  return mock
}

/**
 * Creates a loading mock response
 * @param {Object} query - GraphQL query
 * @param {Object} variables - Query variables
 * @returns {Object} Mock object
 */
export function createLoadingMock(query, variables = {}) {
  return {
    request: {
      query,
      variables
    },
    delay: 1000 // Simulate loading state
  }
}

/**
 * Creates an error mock response
 * @param {Object} query - GraphQL query
 * @param {Object} variables - Query variables
 * @param {String} message - Error message
 * @returns {Object} Mock object
 */
export function createErrorMock(query, variables = {}, message = 'GraphQL error') {
  return createMockResponse(query, variables, null, new Error(message))
}

/**
 * Waits for Apollo operations to complete
 * @param {Number} ms - Milliseconds to wait
 * @returns {Promise}
 */
export function waitForApollo(ms = 0) {
  return new Promise(resolve => setTimeout(resolve, ms))
}
