import { faker } from '@faker-js/faker'

/**
 * Creates a mock company object
 * @param {Object} overrides - Properties to override
 * @returns {Object} Company object
 */
export function createMockCompany(overrides = {}) {
  return {
    id: faker.datatype.uuid(),
    name: faker.company.companyName(),
    address: faker.address.streetAddress(),
    city: faker.address.city(),
    province: faker.address.state(),
    postalCode: faker.address.zipCode(),
    phoneNumber: faker.phone.number(),
    email: faker.internet.email(),
    contactPerson: `${faker.name.firstName()} ${faker.name.lastName()}`,
    organisationId: 1,
    createdAt: faker.date.recent().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
    ...overrides
  }
}

/**
 * Creates multiple mock companies
 * @param {Number} count - Number of companies to create
 * @param {Object} overrides - Properties to override for all companies
 * @returns {Array} Array of company objects
 */
export function createMockCompanies(count = 5, overrides = {}) {
  return Array.from({ length: count }, () => createMockCompany(overrides))
}

/**
 * Mock GraphQL response for company query
 */
export const mockCompanyQueryResponse = {
  company: createMockCompany()
}

/**
 * Mock GraphQL response for companies list query
 */
export const mockCompaniesQueryResponse = {
  companies: createMockCompanies(10)
}

/**
 * Mock company form data
 */
export function createMockCompanyFormData(overrides = {}) {
  return {
    name: faker.company.companyName(),
    address: faker.address.streetAddress(),
    city: faker.address.city(),
    province: faker.address.state(),
    postalCode: faker.address.zipCode(),
    phoneNumber: faker.phone.number(),
    email: faker.internet.email(),
    contactPerson: `${faker.name.firstName()} ${faker.name.lastName()}`,
    ...overrides
  }
}

/**
 * Mock company creation mutation response
 */
export function createMockCompanyMutationResponse(success = true, company = null, errors = []) {
  return {
    createCompany: {
      success,
      errors,
      company: company || createMockCompany()
    }
  }
}

/**
 * Mock company update mutation response
 */
export function createMockCompanyUpdateResponse(success = true, company = null, errors = []) {
  return {
    updateCompany: {
      success,
      errors,
      company: company || createMockCompany()
    }
  }
}

/**
 * Creates a mock clinic object
 * @param {Object} overrides - Properties to override
 * @returns {Object} Clinic object
 */
export function createMockClinic(overrides = {}) {
  return {
    id: faker.datatype.uuid(),
    clinicName: `${faker.company.companyName()} Medical Center`,
    address: faker.address.streetAddress(),
    city: faker.address.city(),
    province: faker.address.state(),
    postalCode: faker.address.zipCode(),
    phoneNumber: faker.phone.number(),
    email: faker.internet.email(),
    contactPerson: `Dr. ${faker.name.firstName()} ${faker.name.lastName()}`,
    organisationId: 1,
    createdAt: faker.date.recent().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
    ...overrides
  }
}

/**
 * Creates multiple mock clinics
 * @param {Number} count - Number of clinics to create
 * @param {Object} overrides - Properties to override for all clinics
 * @returns {Array} Array of clinic objects
 */
export function createMockClinics(count = 3, overrides = {}) {
  return Array.from({ length: count }, () => createMockClinic(overrides))
}

/**
 * Mock GraphQL response for clinics list query
 */
export const mockClinicsQueryResponse = {
  clinics: createMockClinics(5)
}
