import { faker } from '@faker-js/faker'
import { createMockPatient } from './patients'
import { createMockClinic } from './companies'

/**
 * Creates a mock audio assessment object
 * @param {Object} overrides - Properties to override
 * @returns {Object} Audio assessment object
 */
export function createMockAudio(overrides = {}) {
  return {
    id: faker.datatype.uuid(),
    name: `Audio Assessment - ${faker.date.recent().toLocaleDateString()}`,
    datePerformed: faker.date.recent().toISOString().split('T')[0],
    performedBy: `Dr. ${faker.name.firstName()} ${faker.name.lastName()}`,
    result: faker.helpers.arrayElement(['Normal', 'Abnormal', 'Pending']),
    note: faker.lorem.paragraph(),
    systemUsed: faker.helpers.arrayElement(['Audiometer Model A', 'Audiometer Model B']),
    patient: createMockPatient(),
    clinic: createMockClinic(),
    attachments: [],
    createdAt: faker.date.recent().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
    ...overrides
  }
}

/**
 * Creates a mock spirometry assessment object
 * @param {Object} overrides - Properties to override
 * @returns {Object} Spiro assessment object
 */
export function createMockSpiro(overrides = {}) {
  return {
    id: faker.datatype.uuid(),
    name: `Spirometry Assessment - ${faker.date.recent().toLocaleDateString()}`,
    datePerformed: faker.date.recent().toISOString().split('T')[0],
    performedBy: `Dr. ${faker.name.firstName()} ${faker.name.lastName()}`,
    result: faker.helpers.arrayElement(['Normal', 'Abnormal', 'Pending']),
    note: faker.lorem.paragraph(),
    systemUsed: faker.helpers.arrayElement(['Spirometer Model X', 'Spirometer Model Y']),
    patient: createMockPatient(),
    clinic: createMockClinic(),
    attachments: [],
    createdAt: faker.date.recent().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
    ...overrides
  }
}

/**
 * Creates a mock vision screening object
 * @param {Object} overrides - Properties to override
 * @returns {Object} Vision screening object
 */
export function createMockVisionScreening(overrides = {}) {
  return {
    id: faker.datatype.uuid(),
    name: `Vision Screening - ${faker.date.recent().toLocaleDateString()}`,
    datePerformed: faker.date.recent().toISOString().split('T')[0],
    performedBy: `Dr. ${faker.name.firstName()} ${faker.name.lastName()}`,
    result: faker.helpers.arrayElement(['Pass', 'Fail', 'Pending']),
    note: faker.lorem.paragraph(),
    leftEyeResult: faker.helpers.arrayElement(['20/20', '20/30', '20/40']),
    rightEyeResult: faker.helpers.arrayElement(['20/20', '20/30', '20/40']),
    colorVisionResult: faker.helpers.arrayElement(['Normal', 'Deficient']),
    patient: createMockPatient(),
    clinic: createMockClinic(),
    attachments: [],
    createdAt: faker.date.recent().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
    ...overrides
  }
}

/**
 * Creates a mock drug screening object
 * @param {Object} overrides - Properties to override
 * @returns {Object} Drug screening object
 */
export function createMockDrugScreening(overrides = {}) {
  return {
    id: faker.datatype.uuid(),
    reportName: `Drug Screening - ${faker.date.recent().toLocaleDateString()}`,
    datePerformed: faker.date.recent().toISOString().split('T')[0],
    performedBy: `Lab Tech ${faker.name.firstName()} ${faker.name.lastName()}`,
    comment: faker.lorem.paragraph(),

    // Test results
    amphetamines: faker.helpers.arrayElement(['none', 'positive', 'negative']),
    barbiturates: faker.helpers.arrayElement(['none', 'positive', 'negative']),
    benzodiazepines: faker.helpers.arrayElement(['none', 'positive', 'negative']),
    cannabis: faker.helpers.arrayElement(['none', 'positive', 'negative']),
    cocaine: faker.helpers.arrayElement(['none', 'positive', 'negative']),
    opiates: faker.helpers.arrayElement(['none', 'positive', 'negative']),

    patient: createMockPatient(),
    clinic: createMockClinic(),
    attachments: [],
    createdAt: faker.date.recent().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
    ...overrides
  }
}

/**
 * Creates a mock physical assessment object
 * @param {Object} overrides - Properties to override
 * @returns {Object} Physical assessment object
 */
export function createMockPhysicalAssessment(overrides = {}) {
  return {
    id: faker.datatype.uuid(),
    name: `Physical Assessment - ${faker.date.recent().toLocaleDateString()}`,
    datePerformed: faker.date.recent().toISOString().split('T')[0],
    performedBy: `Dr. ${faker.name.firstName()} ${faker.name.lastName()}`,
    result: faker.helpers.arrayElement(['Fit', 'Fit with Restrictions', 'Unfit']),
    note: faker.lorem.paragraph(),
    height: faker.datatype.number({ min: 150, max: 200 }),
    weight: faker.datatype.number({ min: 50, max: 120 }),
    bloodPressure: `${faker.datatype.number({ min: 110, max: 140 })}/${faker.datatype.number({ min: 70, max: 90 })}`,
    heartRate: faker.datatype.number({ min: 60, max: 100 }),
    patient: createMockPatient(),
    clinic: createMockClinic(),
    attachments: [],
    createdAt: faker.date.recent().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
    ...overrides
  }
}

/**
 * Creates multiple mock assessments of a specific type
 * @param {Function} createFunction - Function to create single assessment
 * @param {Number} count - Number of assessments to create
 * @param {Object} overrides - Properties to override for all assessments
 * @returns {Array} Array of assessment objects
 */
export function createMockAssessments(createFunction, count = 5, overrides = {}) {
  return Array.from({ length: count }, () => createFunction(overrides))
}

/**
 * Mock attachment object
 */
export function createMockAttachment(overrides = {}) {
  return {
    id: faker.datatype.uuid(),
    fileName: `${faker.system.fileName()}.pdf`,
    url: faker.internet.url(),
    fileSize: faker.datatype.number({ min: 1000, max: 5000000 }),
    contentType: 'application/pdf',
    createdAt: faker.date.recent().toISOString(),
    ...overrides
  }
}

// Mock GraphQL responses for assessments
export const mockAudioQueryResponse = {
  audio: createMockAudio()
}

export const mockSpiroQueryResponse = {
  spiro: createMockSpiro()
}

export const mockVisionScreeningQueryResponse = {
  visionScreening: createMockVisionScreening()
}

export const mockDrugScreeningQueryResponse = {
  drugScreening: createMockDrugScreening()
}

export const mockPhysicalAssessmentQueryResponse = {
  physicalAssessment: createMockPhysicalAssessment()
}
