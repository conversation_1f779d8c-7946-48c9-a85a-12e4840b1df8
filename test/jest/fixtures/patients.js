import { faker } from '@faker-js/faker'

/**
 * Creates a mock patient object
 * @param {Object} overrides - Properties to override
 * @returns {Object} Patient object
 */
export function createMockPatient(overrides = {}) {
  const firstName = faker.name.firstName()
  const lastName = faker.name.lastName()

  return {
    id: faker.datatype.uuid(),
    firstName,
    lastName,
    email: faker.internet.email(),
    phoneNumber: faker.phone.number(),
    identificationNumber: faker.datatype.number({ min: 1000000000000, max: 9999999999999 }).toString(),
    dob: faker.date.birthdate({ min: 18, max: 65, mode: 'age' }).toISOString().split('T')[0],
    gender: faker.helpers.arrayElement(['Male', 'Female']),
    organisationId: 1,
    createdAt: faker.date.recent().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
    fullName: `${firstName} ${lastName}`,
    employers: [],
    employments: [],
    ...overrides
  }
}

/**
 * Creates multiple mock patients
 * @param {Number} count - Number of patients to create
 * @param {Object} overrides - Properties to override for all patients
 * @returns {Array} Array of patient objects
 */
export function createMockPatients(count = 5, overrides = {}) {
  return Array.from({ length: count }, () => createMockPatient(overrides))
}

/**
 * Creates a mock patient with employment
 * @param {Object} patientOverrides - Patient properties to override
 * @param {Object} employmentOverrides - Employment properties to override
 * @returns {Object} Patient object with employment
 */
export function createMockPatientWithEmployment(patientOverrides = {}, employmentOverrides = {}) {
  const patient = createMockPatient(patientOverrides)

  const employment = {
    id: faker.datatype.uuid(),
    position: faker.name.jobTitle(),
    department: faker.name.jobArea(),
    startDate: faker.date.past().toISOString().split('T')[0],
    endDate: null,
    ...employmentOverrides
  }

  const employer = {
    id: faker.datatype.uuid(),
    name: faker.company.companyName(),
    ...employmentOverrides.employer
  }

  patient.employments = [employment]
  patient.employers = [employer]

  return patient
}

/**
 * Mock GraphQL response for patient query
 */
export const mockPatientQueryResponse = {
  patient: createMockPatient()
}

/**
 * Mock GraphQL response for patients list query
 */
export const mockPatientsQueryResponse = {
  patients: createMockPatients(10)
}

/**
 * Mock GraphQL response for recent patients query
 */
export const mockRecentPatientsQueryResponse = {
  recentPatients: createMockPatients(5)
}

/**
 * Mock patient form data
 */
export function createMockPatientFormData(overrides = {}) {
  return {
    identificationNumber: faker.datatype.number({ min: 1000000000000, max: 9999999999999 }).toString(),
    firstName: faker.name.firstName(),
    lastName: faker.name.lastName(),
    dob: faker.date.birthdate({ min: 18, max: 65, mode: 'age' }).toISOString().split('T')[0],
    gender: 'Male',
    email: faker.internet.email(),
    phoneNumber: faker.phone.number(),
    companyCapture: 'existing',
    companySearch: faker.datatype.uuid(),
    companyInput: '',
    employmentDepartment: faker.name.jobArea(),
    employmentPosition: faker.name.jobTitle(),
    employmentStartDate: faker.date.past().toISOString().split('T')[0],
    ...overrides
  }
}

/**
 * Mock patient creation mutation response
 */
export function createMockPatientMutationResponse(success = true, patient = null, errors = []) {
  return {
    createPatientCaptureForm: {
      success,
      errors,
      patient: patient || createMockPatient()
    }
  }
}

/**
 * Mock patient update mutation response
 */
export function createMockPatientUpdateResponse(success = true, patient = null, errors = []) {
  return {
    patientUpdate: {
      success,
      errors,
      patient: patient || createMockPatient()
    }
  }
}
