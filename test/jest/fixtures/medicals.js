import { faker } from '@faker-js/faker'
import { createMockPatient } from './patients'
import { createMockClinic } from './companies'

/**
 * Creates a mock medical assessment object
 * @param {Object} overrides - Properties to override
 * @returns {Object} Medical object
 */
export function createMockMedical(overrides = {}) {
  const today = new Date()
  const nextYear = new Date(today.getFullYear() + 1, today.getMonth(), today.getDate())

  return {
    id: faker.datatype.uuid(),
    name: `Medical Assessment - ${faker.date.recent().toLocaleDateString()}`,
    medicalType: faker.helpers.arrayElement(['Pre-Employment', 'Annual', 'Exit', 'Fitness for Duty']),
    medicalExaminationDate: today.toISOString().split('T')[0],
    medicalExpiryDate: nextYear.toISOString().split('T')[0],
    status: faker.helpers.arrayElement(['draft', 'completed', 'signed_off']),
    outcome: faker.helpers.arrayElement(['Fit', 'Fit with Restrictions', 'Unfit', 'Pending']),
    outcomeComment: faker.lorem.sentence(),
    referralComment: faker.lorem.sentence(),
    exclusionComment: faker.lorem.sentence(),

    // Assessment flags
    audioPerformed: faker.datatype.boolean(),
    cannabisPerformed: faker.datatype.boolean(),
    ecgPerformed: faker.datatype.boolean(),
    heatPerformed: faker.datatype.boolean(),
    heightPerformed: faker.datatype.boolean(),
    physicalExamPerformed: faker.datatype.boolean(),
    spiroPerformed: faker.datatype.boolean(),
    visualPerformed: faker.datatype.boolean(),
    xrayPerformed: faker.datatype.boolean(),

    patient: createMockPatient(),
    clinic: createMockClinic(),
    assessmentReports: [],

    createdAt: faker.date.recent().toISOString(),
    updatedAt: faker.date.recent().toISOString(),
    ...overrides
  }
}

/**
 * Creates multiple mock medical assessments
 * @param {Number} count - Number of medicals to create
 * @param {Object} overrides - Properties to override for all medicals
 * @returns {Array} Array of medical objects
 */
export function createMockMedicals(count = 5, overrides = {}) {
  return Array.from({ length: count }, () => createMockMedical(overrides))
}

/**
 * Creates a mock assessment report
 * @param {Object} overrides - Properties to override
 * @returns {Object} Assessment report object
 */
export function createMockAssessmentReport(overrides = {}) {
  return {
    id: faker.datatype.uuid(),
    fileName: `${faker.system.fileName()}.pdf`,
    url: faker.internet.url(),
    fileSize: faker.datatype.number({ min: 1000, max: 5000000 }),
    contentType: 'application/pdf',
    createdAt: faker.date.recent().toISOString(),
    ...overrides
  }
}

/**
 * Mock medical form data
 */
export function createMockMedicalFormData(overrides = {}) {
  const today = new Date()
  const nextYear = new Date(today.getFullYear() + 1, today.getMonth(), today.getDate())

  return {
    patientId: faker.datatype.uuid(),
    employmentId: faker.datatype.uuid(),
    clinicId: faker.datatype.uuid(),
    medicalType: 'Pre-Employment',
    name: `Medical Assessment - ${today.toLocaleDateString()}`,

    physicalExamPerformed: false,
    visionScreeningPerformed: false,
    audioPerformed: false,
    spiroPerformed: false,
    xrayPerformed: false,
    ecgPerformed: false,
    heatPerformed: false,
    heightPerformed: false,
    cannabisPerformed: false,

    outcome: 'Fit',
    outcomeComment: false,
    outcomeCommentText: '',
    referral: false,
    referralComment: '',
    exclusion: false,
    exclusionComment: '',

    medicalExaminationDate: today,
    medicalExpiryDate: nextYear,
    ...overrides
  }
}

/**
 * Mock GraphQL response for medical query
 */
export const mockMedicalQueryResponse = {
  medical: createMockMedical()
}

/**
 * Mock GraphQL response for medicals list query
 */
export const mockMedicalsQueryResponse = {
  medicals: createMockMedicals(10)
}

/**
 * Mock GraphQL response for draft medicals query
 */
export const mockDraftMedicalsQueryResponse = {
  draftMedicals: createMockMedicals(5, { status: 'draft' })
}

/**
 * Mock GraphQL response for signed off medicals query
 */
export const mockSignedOffMedicalsQueryResponse = {
  signedOffMedicals: createMockMedicals(5, { status: 'signed_off' })
}

/**
 * Mock medical creation mutation response
 */
export function createMockMedicalMutationResponse(success = true, medical = null, errors = []) {
  return {
    createMedicalCaptureForm: {
      success,
      errors,
      medical: medical || createMockMedical()
    }
  }
}

/**
 * Mock medical update mutation response
 */
export function createMockMedicalUpdateResponse(success = true, medical = null, errors = []) {
  return {
    updateMedical: {
      success,
      errors,
      medical: medical || createMockMedical()
    }
  }
}
