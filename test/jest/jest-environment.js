/**
 * Custom Jest Environment with localStorage mock
 * This ensures localStorage is available before any modules are imported
 */

const JSDOMEnvironment = require('jest-environment-jsdom').default

class CustomJestEnvironment extends JSDOMEnvironment {
  constructor(config, context) {
    super(config, context)

    // Mock localStorage before any modules are imported
    const localStorageMock = {
      getItem: jest.fn(() => null),
      setItem: jest.fn(),
      removeItem: jest.fn(),
      clear: jest.fn(),
      length: 0,
      key: jest.fn(() => null)
    }

    const sessionStorageMock = {
      getItem: jest.fn(() => null),
      setItem: jest.fn(),
      removeItem: jest.fn(),
      clear: jest.fn(),
      length: 0,
      key: jest.fn(() => null)
    }

    // Set up storage mocks on both global and window
    this.global.localStorage = localStorageMock
    this.global.sessionStorage = sessionStorageMock

    if (this.global.window) {
      this.global.window.localStorage = localStorageMock
      this.global.window.sessionStorage = sessionStorageMock
    }

    // Mock other browser APIs
    this.global.fetch = jest.fn(() =>
      Promise.resolve({
        ok: true,
        status: 200,
        statusText: 'OK',
        json: () => Promise.resolve({}),
        text: () => Promise.resolve(''),
        blob: () => Promise.resolve(new Blob()),
        arrayBuffer: () => Promise.resolve(new ArrayBuffer(0))
      })
    )

    // Mock console to reduce noise
    this.global.console = {
      ...this.global.console,
      log: jest.fn(),
      debug: jest.fn(),
      info: jest.fn(),
      warn: jest.fn(),
      error: jest.fn()
    }
  }

  async setup() {
    await super.setup()
  }

  async teardown() {
    await super.teardown()
  }
}

module.exports = CustomJestEnvironment
