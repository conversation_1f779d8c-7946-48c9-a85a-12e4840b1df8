// Mock localStorage and sessionStorage BEFORE any other imports
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn()
}

const sessionStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
  length: 0,
  key: jest.fn()
}

// Set up global mocks before any modules are imported
Object.defineProperty(global, 'localStorage', {
  value: localStorageMock,
  writable: true
})

Object.defineProperty(global, 'sessionStorage', {
  value: sessionStorageMock,
  writable: true
})

// Mock window object if it doesn't exist
if (typeof window === 'undefined') {
  global.window = {
    localStorage: localStorageMock,
    sessionStorage: sessionStorageMock,
    dispatchEvent: jest.fn(),
    addEventListener: jest.fn(),
    removeEventListener: jest.fn()
  }
} else {
  window.localStorage = localStorageMock
  window.sessionStorage = sessionStorageMock
}

// Simplified Jest setup for Vue 3 components
// Mock Quasar components and plugins
global.Quasar = {
  Dialog: {
    create: jest.fn()
  },
  Notify: {
    create: jest.fn()
  },
  Loading: {
    show: jest.fn(),
    hide: jest.fn()
  }
}

// Mock Vue Router
global.$router = {
  push: jest.fn(),
  replace: jest.fn(),
  go: jest.fn(),
  back: jest.fn()
}

global.$route = {
  params: {},
  query: {},
  path: '/'
}

// Mock environment variables
process.env.VITE_GRAPHQL_ENDPOINT = 'http://localhost:3001/graphql'

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn()
}

// Mock window.matchMedia for responsive components
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: jest.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: jest.fn(), // deprecated
    removeListener: jest.fn(), // deprecated
    addEventListener: jest.fn(),
    removeEventListener: jest.fn(),
    dispatchEvent: jest.fn(),
  })),
})

// Mock IntersectionObserver
global.IntersectionObserver = class IntersectionObserver {
  constructor() { }
  disconnect() { }
  observe() { }
  unobserve() { }
}

// Mock ResizeObserver
global.ResizeObserver = class ResizeObserver {
  constructor() { }
  disconnect() { }
  observe() { }
  unobserve() { }
}
