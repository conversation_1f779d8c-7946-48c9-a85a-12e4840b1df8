/**
 * Mock for use-offline-mode composable
 * This prevents localStorage access during module loading
 */

import { ref, computed } from 'vue'

// Mock global offline state
const isOfflineMode = ref(false)
const offlineStartTime = ref(null)

export function useOfflineMode() {
  const toggleOfflineMode = jest.fn(() => {
    isOfflineMode.value = !isOfflineMode.value
    if (isOfflineMode.value) {
      offlineStartTime.value = new Date()
    } else {
      offlineStartTime.value = null
    }
  })

  const setOfflineMode = jest.fn((offline) => {
    isOfflineMode.value = offline
    if (offline) {
      offlineStartTime.value = new Date()
    } else {
      offlineStartTime.value = null
    }
  })

  const restoreOfflineState = jest.fn()

  const offlineDuration = computed(() => {
    if (!isOfflineMode.value || !offlineStartTime.value) return 0
    return Math.round((new Date() - offlineStartTime.value) / 1000)
  })

  const formattedOfflineDuration = computed(() => {
    const seconds = offlineDuration.value
    if (seconds < 60) return `${seconds}s`
    const minutes = Math.floor(seconds / 60)
    const remainingSeconds = seconds % 60
    return `${minutes}m ${remainingSeconds}s`
  })

  const shouldBlockRequest = jest.fn(() => false)
  const createNetworkError = jest.fn((url) => new Error(`Network error: ${url}`))
  const handleApolloRequest = jest.fn((operation, forward) => forward(operation))

  return {
    // State
    isOfflineMode: computed(() => isOfflineMode.value),
    offlineStartTime: computed(() => offlineStartTime.value),
    offlineDuration,
    formattedOfflineDuration,
    
    // Actions
    toggleOfflineMode,
    setOfflineMode,
    restoreOfflineState,
    
    // Utilities
    shouldBlockRequest,
    createNetworkError,
    handleApolloRequest,
  }
}
