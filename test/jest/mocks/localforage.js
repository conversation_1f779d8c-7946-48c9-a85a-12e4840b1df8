/**
 * Mock for localforage
 * This prevents localStorage/IndexedDB access during testing
 */

const localforageMock = {
  getItem: jest.fn(() => Promise.resolve(null)),
  setItem: jest.fn(() => Promise.resolve()),
  removeItem: jest.fn(() => Promise.resolve()),
  clear: jest.fn(() => Promise.resolve()),
  length: jest.fn(() => Promise.resolve(0)),
  key: jest.fn(() => Promise.resolve(null)),
  keys: jest.fn(() => Promise.resolve([])),
  iterate: jest.fn(() => Promise.resolve()),
  
  // Configuration methods
  config: jest.fn(),
  defineDriver: jest.fn(),
  driver: jest.fn(() => 'mockDriver'),
  ready: jest.fn(() => Promise.resolve()),
  
  // Create instance
  createInstance: jest.fn(() => localforageMock),
  
  // Constants
  WEBSQL: 'websqldb',
  INDEXEDDB: 'asyncStorage',
  LOCALSTORAGE: 'localStorageWrapper'
}

module.exports = localforageMock
