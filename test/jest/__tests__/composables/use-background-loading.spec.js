import { describe, it, expect, jest, beforeEach } from '@jest/globals'

// Mock the entire use-background-loading module to avoid Vue dependency issues
jest.mock('src/composables/use-background-loading', () => {
  const { ref, computed } = jest.requireActual('vue')
  
  return {
    useBackgroundLoading: jest.fn(() => ({
      addBackgroundQuery: jest.fn(),
      removeBackgroundQuery: jest.fn(),
      isBackgroundLoading: computed(() => false),
      backgroundLoadingCount: computed(() => 0)
    })),
    provideBackgroundLoading: jest.fn(() => ({
      addBackgroundQuery: jest.fn(),
      removeBackgroundQuery: jest.fn(),
      isBackgroundLoading: computed(() => false),
      backgroundLoadingCount: computed(() => 0)
    })),
    generateQueryId: jest.fn(() => 'test-query-id'),
    BACKGROUND_LOADING_KEY: Symbol('background-loading')
  }
})

describe('useBackgroundLoading', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('should import and use the mocked composable', () => {
    const { useBackgroundLoading } = require('src/composables/use-background-loading')
    const result = useBackgroundLoading()
    
    expect(result.addBackgroundQuery).toBeDefined()
    expect(result.removeBackgroundQuery).toBeDefined()
    expect(result.isBackgroundLoading).toBeDefined()
    expect(result.backgroundLoadingCount).toBeDefined()
    expect(typeof result.addBackgroundQuery).toBe('function')
    expect(typeof result.removeBackgroundQuery).toBe('function')
  })

  it('should have mocked provideBackgroundLoading', () => {
    const { provideBackgroundLoading } = require('src/composables/use-background-loading')
    const result = provideBackgroundLoading()
    
    expect(result.addBackgroundQuery).toBeDefined()
    expect(result.removeBackgroundQuery).toBeDefined()
    expect(result.isBackgroundLoading).toBeDefined()
    expect(result.backgroundLoadingCount).toBeDefined()
  })

  it('should have mocked generateQueryId', () => {
    const { generateQueryId } = require('src/composables/use-background-loading')
    const result = generateQueryId('test', {})
    
    expect(result).toBe('test-query-id')
  })

  it('should have BACKGROUND_LOADING_KEY', () => {
    const { BACKGROUND_LOADING_KEY } = require('src/composables/use-background-loading')
    
    expect(typeof BACKGROUND_LOADING_KEY).toBe('symbol')
  })
})
