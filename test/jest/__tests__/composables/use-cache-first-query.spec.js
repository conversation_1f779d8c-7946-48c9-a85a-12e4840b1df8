import { describe, it, expect, beforeEach, jest } from '@jest/globals'
import { ref, nextTick } from 'vue'
import { useCacheFirstQuery } from 'src/composables/use-cache-first-query'

// Mock the Apollo composable
jest.mock('@vue/apollo-composable', () => ({
  useQuery: jest.fn(),
  DefaultApolloClient: Symbol('DefaultApolloClient')
}))

// Mock the background loading composable
jest.mock('src/composables/use-background-loading', () => ({
  useBackgroundLoading: jest.fn(),
  generateQueryId: jest.fn(() => 'test-query-id')
}))

// Mock Vue's inject function and lifecycle hooks
jest.mock('vue', () => {
  const actual = jest.requireActual('vue')
  return {
    ...actual,
    inject: jest.fn(),
    onMounted: jest.fn((callback) => {
      // Execute the callback immediately in tests
      if (typeof callback === 'function') {
        callback()
      }
    }),
    onUnmounted: jest.fn()
  }
})

describe('useCacheFirstQuery', () => {
  let mockUseQuery
  let mockBackgroundLoading
  let mockApolloClient

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks()

    // Mock useQuery return value
    mockUseQuery = {
      result: ref(null),
      loading: ref(false),
      error: ref(null),
      refetch: jest.fn(),
      networkStatus: ref(7) // 7 = ready
    }

    // Mock background loading
    mockBackgroundLoading = {
      addBackgroundQuery: jest.fn(),
      removeBackgroundQuery: jest.fn()
    }

    // Mock Apollo client
    mockApolloClient = {
      readQuery: jest.fn()
    }

    // Setup mocks
    const { useQuery } = require('@vue/apollo-composable')
    const { useBackgroundLoading } = require('src/composables/use-background-loading')
    const { inject } = require('vue')

    useQuery.mockReturnValue(mockUseQuery)
    useBackgroundLoading.mockReturnValue(mockBackgroundLoading)
    inject.mockReturnValue(mockApolloClient)
  })

  it('should initialize with correct default values', () => {
    const query = 'query Test { test }'
    const variables = { id: 1 }

    const result = useCacheFirstQuery(query, variables)

    expect(result).toHaveProperty('result')
    expect(result).toHaveProperty('loading')
    expect(result).toHaveProperty('isRefreshing')
    expect(result).toHaveProperty('isBackgroundRefresh')
    expect(result).toHaveProperty('error')
    expect(result).toHaveProperty('refetch')
    expect(result).toHaveProperty('hasCachedData')
    expect(result).toHaveProperty('queryId')
  })

  it('should call useQuery with correct cache-first options', () => {
    const { useQuery } = require('@vue/apollo-composable')
    const query = 'query Test { test }'
    const variables = { id: 1 }
    const options = { someOtherOption: 'test' }

    useCacheFirstQuery(query, variables, options)

    expect(useQuery).toHaveBeenCalledWith(
      query,
      variables,
      expect.objectContaining({
        fetchPolicy: 'cache-first',
        nextFetchPolicy: 'cache-and-network',
        notifyOnNetworkStatusChange: true,
        errorPolicy: 'all',
        ...options
      })
    )
  })

  it('should track background loading when refreshing with cached data', async () => {
    const query = 'query Test { test }'
    const variables = { id: 1 }

    // Setup initial state with cached data
    mockUseQuery.result.value = { test: 'data' }
    mockUseQuery.loading.value = false

    const result = useCacheFirstQuery(query, variables)

    // Wait for initial setup
    await nextTick()

    // Simulate background refresh
    mockUseQuery.loading.value = true
    mockUseQuery.networkStatus.value = 4 // refetch

    // Wait for reactivity
    await nextTick()

    expect(mockBackgroundLoading.addBackgroundQuery).toHaveBeenCalledWith('test-query-id')
  })

  it('should remove background loading when refresh completes', async () => {
    const query = 'query Test { test }'
    const variables = { id: 1 }

    // Setup initial state with cached data and loading
    mockUseQuery.result.value = { test: 'data' }
    mockUseQuery.loading.value = true
    mockUseQuery.networkStatus.value = 4 // refetch

    const result = useCacheFirstQuery(query, variables)

    // Wait for initial setup
    await nextTick()

    // Simulate refresh completion
    mockUseQuery.loading.value = false
    mockUseQuery.networkStatus.value = 7 // ready

    // Wait for reactivity
    await nextTick()

    expect(mockBackgroundLoading.removeBackgroundQuery).toHaveBeenCalledWith('test-query-id')
  })

  it('should handle cache read errors gracefully', async () => {
    const query = 'query Test { test }'
    const variables = { id: 1 }

    // Mock cache read failure
    mockApolloClient.readQuery.mockImplementation(() => {
      throw new Error('Cache read failed')
    })

    const result = useCacheFirstQuery(query, variables)

    // Wait for mounted hook to execute
    await nextTick()

    expect(mockApolloClient.readQuery).toHaveBeenCalledWith({
      query,
      variables
    })
    expect(mockUseQuery.refetch).toHaveBeenCalled()
  })

  it('should compute loading states correctly', () => {
    const query = 'query Test { test }'
    const variables = { id: 1 }

    // Test initial loading state
    mockUseQuery.loading.value = true
    mockUseQuery.result.value = null

    const result = useCacheFirstQuery(query, variables)

    expect(result.loading.value).toBe(true) // isInitialLoad
    expect(result.isRefreshing.value).toBe(false)

    // Test refreshing state
    mockUseQuery.result.value = { test: 'data' }
    result.hasCachedData.value = true

    expect(result.loading.value).toBe(false) // not initial load anymore
    expect(result.isRefreshing.value).toBe(true) // has cached data and loading
  })
})
