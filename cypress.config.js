const { defineConfig } = require('cypress')

module.exports = defineConfig({
  fixturesFolder: 'test/cypress/fixtures',
  screenshotsFolder: 'test/cypress/screenshots',
  videosFolder: 'test/cypress/videos',
  video: true,
  viewportWidth: 1280,
  viewportHeight: 720,
  experimentalSessionAndOrigin: true,
  e2e: {
    // We've imported your old cypress plugins here.
    // You may want to clean this up later by importing these.
    setupNodeEvents(on, config) {
      return require('./test/cypress/plugins/index.js')(on, config)
    },
    baseUrl: 'http://localhost:9002/',
    specPattern: 'test/cypress/integration/**/*.spec.js',
    supportFile: 'test/cypress/support/index.js',
  },
})
