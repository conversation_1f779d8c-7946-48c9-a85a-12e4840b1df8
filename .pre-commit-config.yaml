repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-json
      - id: check-merge-conflict
      - id: check-added-large-files
        args: ['--maxkb=1000']
      - id: mixed-line-ending
        args: ['--fix=lf']

  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.44.0
    hooks:
      - id: eslint
        files: \.(js|vue)$
        types: [file]
        additional_dependencies:
          - eslint@8.44.0
          - eslint-plugin-vue@9.15.1
          - '@vue/eslint-config-standard'

  - repo: local
    hooks:
      - id: jest-unit-tests
        name: Jest Unit Tests
        entry: npm run test:unit
        language: system
        pass_filenames: false
        stages: [commit]
        
      - id: jest-coverage-check
        name: Jest Coverage Check
        entry: npm run test:unit:coverage
        language: system
        pass_filenames: false
        stages: [commit]
        
      - id: vue-type-check
        name: Vue Type Check
        entry: npm run type-check
        language: system
        pass_filenames: false
        files: \.(vue|js|ts)$
        stages: [commit]

  - repo: https://github.com/commitizen-tools/commitizen
    rev: v3.5.2
    hooks:
      - id: commitizen
        stages: [commit-msg]
